# 多摄像头插件自动降级策略优化

## 优化概述

本次优化针对多摄像头插件的自动降级策略进行了智能化改进，主要解决了以下问题：

1. **过度降级**：原策略对所有错误都进行降级，包括权限错误
2. **缺乏错误分类**：没有区分不同类型错误的处理方式
3. **递归风险**：拍照重试使用递归调用可能导致栈溢出
4. **缺乏延迟机制**：连续失败时没有冷却时间

## 优化后的策略

### 1. 智能错误分类

新增了 `ErrorClassifier` 类，将错误分为三个严重程度：

#### 严重错误 (Severe)
- **特征**：需要用户干预，不应自动降级
- **包含**：权限错误、授权失败
- **处理**：直接抛出错误，提示用户处理
- **示例**：`PERMISSION_DENIED`, `not authorized`

#### 轻微错误 (Minor)  
- **特征**：临时性问题，可以重试
- **包含**：摄像头被占用、存储空间不足、网络问题
- **处理**：延迟后重试当前实现
- **示例**：`CAMERA_IN_USE`, `storage full`

#### 中等错误 (Moderate)
- **特征**：硬件或兼容性问题，需要降级
- **包含**：硬件故障、相机黑屏、初始化失败
- **处理**：切换到备用实现方案
- **示例**：`HARDWARE_ERROR`, `black screen`, `INITIALIZATION_FAILED`

### 2. 优化的配置参数

```dart
const CameraImplementationPriority({
  this.enableAutoFallback = true,
  this.maxRetryCount = 2,           // 降低重试次数
  this.retryDelayMs = 500,          // 添加重试延迟
  this.enableSmartErrorHandling = true, // 启用智能错误处理
});
```

### 3. 改进的处理流程

#### 初始化失败处理
```dart
// 1. 错误分类
final severity = ErrorClassifier.classifyError(error.code, error.message);

// 2. 根据严重程度处理
switch (severity) {
  case ErrorSeverity.severe:
    // 直接抛出，不降级
    throw error;
    
  case ErrorSeverity.minor:
    // 延迟后重试当前实现
    await Future.delayed(Duration(milliseconds: retryDelayMs));
    return await initialize(...);
    
  case ErrorSeverity.moderate:
    // 尝试降级到备用实现
    await _tryFallbackImplementation();
}
```

#### 拍照失败处理
- 移除递归调用风险
- 降级成功后重置重试计数
- 添加延迟机制避免频繁重试

#### 预览错误处理
- 轻微错误不主动重试，让系统自然恢复
- 严重错误直接处理，不降级
- 中等错误才进行降级

## 常见场景处理

### 1. 相机黑屏问题
- **分类**：中等错误
- **处理**：自动切换到备用实现
- **原因**：通常是硬件兼容性问题

### 2. 权限被拒绝
- **分类**：严重错误  
- **处理**：直接提示用户授权
- **原因**：需要用户主动操作

### 3. 摄像头被占用
- **分类**：轻微错误
- **处理**：延迟500ms后重试
- **原因**：其他应用可能很快释放摄像头

### 4. 硬件初始化失败
- **分类**：中等错误
- **处理**：切换实现方案
- **原因**：当前实现可能不兼容设备

## 使用示例

### 基本使用（默认智能处理）
```dart
final manager = MultiCameraManager();
await manager.initialize(
  config: CameraConfig(
    lensDirection: CameraLensDirection.back,
    enableAutoInitialization: true,
  ),
  // 使用默认的智能降级策略
);
```

### 自定义降级策略
```dart
await manager.initialize(
  config: config,
  priority: CameraImplementationPriority(
    enableAutoFallback: true,
    maxRetryCount: 1,              // 减少重试次数
    retryDelayMs: 1000,           // 增加延迟时间
    enableSmartErrorHandling: true, // 启用智能处理
  ),
);
```

### 监听降级事件
```dart
manager.eventStream.listen((event) {
  if (event is CameraImplementationSwitchedEvent) {
    print('自动降级: ${event.oldImplementation} -> ${event.newImplementation}');
    print('原因: ${event.reason}');
  }
});
```

## 优化效果

1. **减少不必要的降级**：权限错误不再触发降级
2. **提高成功率**：轻微错误通过重试解决
3. **避免栈溢出**：移除递归调用
4. **更好的用户体验**：智能区分错误类型，给出合适的处理方式
5. **降低资源消耗**：减少无效的重试和降级操作

## 向后兼容性

- 所有现有API保持不变
- 默认启用智能错误处理
- 可通过配置禁用新功能，回到原有行为
- 不影响现有的错误监听和处理逻辑

## 建议

1. **生产环境**：建议启用智能错误处理
2. **测试阶段**：可以通过事件监听观察降级行为
3. **特殊需求**：可以自定义重试次数和延迟时间
4. **错误处理**：建议监听错误事件，为用户提供友好的提示
