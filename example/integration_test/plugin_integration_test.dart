// This is a basic Flutter integration test.
//
// Since integration tests run in a full Flutter application, they can interact
// with the host side of a plugin implementation, unlike Dart unit tests.
//
// For more information about Flutter integration tests, please see
// https://flutter.dev/to/integration-testing

import 'package:flutter_test/flutter_test.dart';
import 'package:integration_test/integration_test.dart';

import 'package:multi_camera_plugin/multi_camera_plugin.dart';

void main() {
  IntegrationTestWidgetsFlutterBinding.ensureInitialized();

  testWidgets('MultiCameraPlugin initialization test',
      (WidgetTester tester) async {
    final MultiCameraPlugin plugin = MultiCameraPlugin.instance;

    // 测试插件实例创建
    expect(plugin, isNotNull);
    expect(plugin.status, CameraStatus.uninitialized);
    expect(plugin.currentImplementation, isNull);
    expect(plugin.availableImplementations, isEmpty);
  });

  testWidgets('CameraConfig creation test', (WidgetTester tester) async {
    final config = CameraConfig(
      lensDirection: CameraLensDirection.back,
      enableAudio: false,
      imageQuality: 0.8,
      isCircular: false,
      isSquare: false,
      enableAutoInitialization: false,
    );

    expect(config.lensDirection, CameraLensDirection.back);
    expect(config.enableAudio, false);
    expect(config.imageQuality, 0.8);
    expect(config.isCircular, false);
    expect(config.isSquare, false);
    expect(config.enableAutoInitialization, false);
  });
}
