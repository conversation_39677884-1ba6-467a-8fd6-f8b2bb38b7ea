pluginManagement {
    def flutterSdkPath = {
        def properties = new Properties()
        file("local.properties").withInputStream { properties.load(it) }
        def flutterSdkPath = properties.getProperty("flutter.sdk")
        assert flutterSdkPath != null, "flutter.sdk not set in local.properties"
        return flutterSdkPath
    }()

    includeBuild("$flutterSdkPath/packages/flutter_tools/gradle")

    repositories {
        google()
        mavenCentral()
        gradlePluginPortal()
    }
}

plugins {
    id "dev.flutter.flutter-plugin-loader" version "1.0.0"
    id "com.android.application" version "8.1.0" apply false
    id "org.jetbrains.kotlin.android" version "1.8.22" apply false
}

include ":app"

// Workaround to add namespace for old camerawesome version
gradle.beforeProject { project ->
    if (project.name == "camerawesome") {
        project.buildFile.withReader { reader ->
            def text = reader.text
            if (!text.contains("namespace")) {
                project.buildFile.withWriter { writer ->
                    def lines = text.split('\n').toList()
                    def androidIndex = lines.findIndexOf { it.contains("android {") }
                    if (androidIndex != -1) {
                        lines.add(androidIndex + 1, "    namespace 'com.apparence.camerawesome'")
                        writer.write(lines.join('\n'))
                    }
                }
            }
        }
    }
}
