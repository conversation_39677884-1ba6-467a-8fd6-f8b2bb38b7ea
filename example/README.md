# Multi Camera Plugin 示例应用

这是一个完整的示例应用，展示了如何使用Multi Camera Plugin的所有功能。

## 功能演示

### 🎯 核心功能
- ✅ 多种摄像头实现方案切换
- ✅ 自动降级和手动切换
- ✅ 前后摄像头切换
- ✅ 闪光灯控制（关闭/开启/自动/常亮）
- ✅ 高质量拍照
- ✅ 实时状态监控
- ✅ 错误处理和用户反馈

### 📱 用户界面
- **状态面板**: 显示当前摄像头状态、实现方案、方向等信息
- **预览窗口**: 实时摄像头预览，支持状态和实现方案指示器
- **控制按钮**: 初始化、拍照、切换摄像头、闪光灯控制等
- **实现方案选择**: 弹窗选择不同的摄像头实现方案

## 运行示例

### 前置条件
1. Flutter SDK 3.3.0+
2. Android Studio / Xcode
3. 真机设备（推荐，模拟器摄像头功能有限）

### 安装步骤

1. **克隆项目**
```bash
git clone <repository-url>
cd multi_camera_plugin/example
```

2. **安装依赖**
```bash
flutter pub get
```

3. **配置权限**

**Android** (`android/app/src/main/AndroidManifest.xml`):
```xml
<uses-permission android:name="android.permission.CAMERA" />
<uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
<uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
```

**iOS** (`ios/Runner/Info.plist`):
```xml
<key>NSCameraUsageDescription</key>
<string>此应用需要访问摄像头来拍照</string>
<key>NSPhotoLibraryUsageDescription</key>
<string>此应用需要访问相册来保存照片</string>
```

4. **运行应用**
```bash
flutter run
```

## 使用指南

### 基本操作流程

1. **启动应用**: 应用启动后会显示摄像头状态为"未初始化"
2. **初始化摄像头**: 点击"初始化"按钮开始摄像头初始化
3. **查看预览**: 初始化成功后会显示摄像头预览
4. **拍照**: 点击"拍照"按钮进行拍照
5. **切换功能**: 使用各种切换按钮测试不同功能

### 高级功能测试

#### 1. 实现方案切换
- 点击"切换实现"按钮
- 在弹出的对话框中选择不同的实现方案
- 观察切换过程和性能差异

#### 2. 自动降级测试
- 在某些设备上，某个实现方案可能会失败
- 插件会自动切换到备用方案
- 观察状态面板中的切换信息

#### 3. 错误处理测试
- 尝试在没有权限的情况下初始化摄像头
- 观察错误信息和恢复机制
- 测试各种异常情况的处理

### 状态指示器说明

#### 摄像头状态
- **未初始化**: 摄像头尚未初始化
- **初始化中**: 正在初始化摄像头
- **已就绪**: 摄像头已准备就绪，可以拍照
- **拍照中**: 正在执行拍照操作
- **错误**: 摄像头出现错误
- **已释放**: 摄像头资源已释放

#### 实现方案
- **Flutter Camera**: 使用官方Flutter Camera插件
- **原生实现**: 使用平台原生摄像头API

## 代码结构

### 主要文件
```
example/
├── lib/
│   └── main.dart              # 主应用代码
├── android/
│   └── app/src/main/
│       └── AndroidManifest.xml # Android权限配置
├── ios/
│   └── Runner/
│       └── Info.plist         # iOS权限配置
└── README.md                  # 本文件
```

### 核心代码片段

#### 初始化摄像头
```dart
Future<void> _initializeCamera() async {
  try {
    final config = CameraConfig(
      lensDirection: _currentDirection,
      enableAudio: false,
      imageQuality: 0.8,
      isCircular: false,
      isSquare: false,
    );

    await _plugin.initialize(config: config);
    setState(() {
      _statusMessage = '摄像头初始化成功';
    });
  } catch (e) {
    setState(() {
      _statusMessage = '初始化失败: $e';
    });
  }
}
```

#### 事件监听
```dart
void _setupListeners() {
  // 监听状态变化
  _plugin.statusStream.listen((status) {
    setState(() {
      _status = status;
    });
  });

  // 监听实现方案切换
  _plugin.eventStream.listen((event) {
    if (event is CameraImplementationSwitchedEvent) {
      _showSnackBar(
        '实现方案已切换: \${event.oldImplementation} → \${event.newImplementation}',
      );
    }
  });
}
```

#### 预览组件
```dart
MultiCameraPreview(
  cameraConfig: config,
  previewConfig: const CameraPreviewConfig(),
  showStatusIndicator: true,
  showImplementationIndicator: true,
  onCameraReady: () => print('摄像头就绪'),
  onError: (error) => print('错误: \${error.message}'),
)
```

## 测试建议

### 设备测试
建议在以下设备上测试：
- **Android**: 不同厂商的设备（Samsung、Huawei、Xiaomi等）
- **iOS**: 不同版本的iPhone和iPad
- **版本**: 不同Android版本（API 21+）和iOS版本（12.0+）

### 功能测试清单
- [ ] 摄像头初始化
- [ ] 前后摄像头切换
- [ ] 拍照功能
- [ ] 闪光灯控制
- [ ] 实现方案手动切换
- [ ] 自动降级机制
- [ ] 权限处理
- [ ] 错误恢复
- [ ] 内存管理
- [ ] 生命周期处理

### 性能测试
- 启动时间
- 预览延迟
- 拍照速度
- 内存使用
- 电池消耗

## 故障排除

### 常见问题

1. **权限被拒绝**
   - 检查AndroidManifest.xml和Info.plist配置
   - 手动在设备设置中授予权限

2. **预览黑屏**
   - 尝试切换实现方案
   - 重启应用
   - 检查设备摄像头是否被其他应用占用

3. **拍照失败**
   - 确保存储权限已授予
   - 检查存储空间
   - 尝试不同的实现方案

4. **应用崩溃**
   - 查看控制台日志
   - 确保在真机上测试
   - 检查Flutter和插件版本兼容性

### 调试技巧

1. **启用详细日志**
```dart
// 在main.dart中添加
void main() {
  // 启用Flutter日志
  debugPrint('Multi Camera Plugin Example Started');
  runApp(const MyApp());
}
```

2. **监控所有事件**
```dart
_plugin.eventStream.listen((event) {
  print('Camera Event: \${event.runtimeType} - \$event');
});
```

3. **检查设备能力**
```dart
final capabilities = await _plugin.manager.getCameraCapabilities();
print('Device capabilities: \$capabilities');
```

## 反馈和贡献

如果您在使用示例应用时遇到问题或有改进建议，请：

1. 提交Issue到GitHub仓库
2. 提供详细的设备信息和错误日志
3. 描述重现步骤
4. 建议改进方案

感谢您的使用和反馈！
