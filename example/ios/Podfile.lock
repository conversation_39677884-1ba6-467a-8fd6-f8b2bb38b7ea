PODS:
  - camera_avfoundation (0.0.1):
    - Flutter
  - Flutter (1.0.0)
  - integration_test (0.0.1):
    - Flutter
  - multi_camera_plugin (1.0.0):
    - Flutter
  - path_provider_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - permission_handler_apple (9.3.0):
    - Flutter
  - shared_preferences_foundation (0.0.1):
    - Flutter
    - FlutterMacOS

DEPENDENCIES:
  - camera_avfoundation (from `.symlinks/plugins/camera_avfoundation/ios`)
  - Flutter (from `Flutter`)
  - integration_test (from `.symlinks/plugins/integration_test/ios`)
  - multi_camera_plugin (from `.symlinks/plugins/multi_camera_plugin/ios`)
  - path_provider_foundation (from `.symlinks/plugins/path_provider_foundation/darwin`)
  - permission_handler_apple (from `.symlinks/plugins/permission_handler_apple/ios`)
  - shared_preferences_foundation (from `.symlinks/plugins/shared_preferences_foundation/darwin`)

EXTERNAL SOURCES:
  camera_avfoundation:
    :path: ".symlinks/plugins/camera_avfoundation/ios"
  Flutter:
    :path: Flutter
  integration_test:
    :path: ".symlinks/plugins/integration_test/ios"
  multi_camera_plugin:
    :path: ".symlinks/plugins/multi_camera_plugin/ios"
  path_provider_foundation:
    :path: ".symlinks/plugins/path_provider_foundation/darwin"
  permission_handler_apple:
    :path: ".symlinks/plugins/permission_handler_apple/ios"
  shared_preferences_foundation:
    :path: ".symlinks/plugins/shared_preferences_foundation/darwin"

SPEC CHECKSUMS:
  camera_avfoundation: adb0207d868b2d873e895371d88448399ab78d87
  Flutter: e0871f40cf51350855a761d2e70bf5af5b9b5de7
  integration_test: 252f60fa39af5e17c3aa9899d35d908a0721b573
  multi_camera_plugin: 7707696a734e41393a9deb2a210807c3c24515d5
  path_provider_foundation: 2b6b4c569c0fb62ec74538f866245ac84301af46
  permission_handler_apple: 9878588469a2b0d0fc1e048d9f43605f92e6cec2
  shared_preferences_foundation: fcdcbc04712aee1108ac7fda236f363274528f78

PODFILE CHECKSUM: 5d2cd35272fce7f0e72691478139ce56d1c2f440

COCOAPODS: 1.13.0
