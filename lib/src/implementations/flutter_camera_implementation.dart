import 'dart:io';
import 'package:camera/camera.dart' as camera;
import 'package:flutter/material.dart';
import '../models/camera_models.dart';

/// Flutter Camera插件实现
///
/// 这是Flutter官方提供的摄像头插件，具有良好的稳定性和性能。
/// 适用于大多数iOS和Android设备。
class FlutterCameraImplementation {
  static camera.CameraController? _controller;
  static CameraConfig? _config;
  static List<camera.CameraDescription>? _cameras;
  static bool _isInitialized = false;
  static CameraLensDirection _currentDirection = CameraLensDirection.back;
  static FlashMode _currentFlashMode = FlashMode.off;
  static bool _callbacksSetup = false;

  /// 检查Flutter Camera是否可用
  static Future<bool> isAvailable() async {
    try {
      debugPrint('🔍 FlutterCameraImplementation: 检查可用性');

      // 检查平台支持
      if (!Platform.isIOS && !Platform.isAndroid) {
        debugPrint('🔍 FlutterCamera 平台不支持: ${Platform.operatingSystem}');
        return false;
      }

      final cameras = await camera.availableCameras();
      final isAvailable = cameras.isNotEmpty;
      debugPrint(
          '🔍 FlutterCamera 可用性检查结果: $isAvailable (找到 ${cameras.length} 个摄像头)');

      // 打印找到的摄像头信息
      for (int i = 0; i < cameras.length; i++) {
        final cam = cameras[i];
        print('🔍   摄像头 $i: ${cam.name}, 方向: ${cam.lensDirection}');
      }

      return isAvailable;
    } catch (e) {
      debugPrint('❌ FlutterCamera 可用性检查失败: $e');
      return false;
    }
  }

  /// 初始化摄像头
  static Future<void> initialize(CameraConfig config) async {
    debugPrint('📷 FlutterCameraImplementation: 开始初始化');
    debugPrint('📷 当前初始化状态: $_isInitialized');

    if (_isInitialized) {
      debugPrint('📷 已初始化，先释放资源');
      await dispose();
    }

    try {
      debugPrint('📷 获取可用摄像头列表...');
      _cameras = await camera.availableCameras();
      debugPrint('📷 找到摄像头数量: ${_cameras?.length ?? 0}');

      if (_cameras == null || _cameras!.isEmpty) {
        debugPrint('❌ 未找到可用的摄像头');
        throw CameraError(
          code: 'NO_CAMERAS',
          message: '未找到可用的摄像头',
        );
      }

      _config = config;
      _currentDirection = config.lensDirection;
      debugPrint('📷 配置摄像头方向: ${config.lensDirection}');

      // 查找指定方向的摄像头
      final cameraDesc = _findCamera(config.lensDirection);
      if (cameraDesc == null) {
        debugPrint('❌ 未找到指定方向的摄像头: ${config.lensDirection}');
        throw CameraError(
          code: 'CAMERA_NOT_FOUND',
          message: '未找到指定方向的摄像头',
        );
      }

      debugPrint(
          '📷 使用摄像头: ${cameraDesc.name}, 方向: ${cameraDesc.lensDirection}');

      // 创建摄像头控制器
      debugPrint('📷 创建摄像头控制器...');
      _controller = camera.CameraController(
        cameraDesc,
        camera.ResolutionPreset.high,
        enableAudio: config.enableAudio,
        imageFormatGroup: camera.ImageFormatGroup.jpeg,
      );

      // 初始化控制器
      debugPrint('📷 初始化摄像头控制器...');
      await _controller!.initialize();
      debugPrint('📷 摄像头控制器初始化完成');

      // 设置闪光灯模式 (仅在支持的平台上)
      if (Platform.isIOS || Platform.isAndroid) {
        debugPrint('📷 设置闪光灯模式: $_currentFlashMode');
        await setFlashMode(_currentFlashMode, duringInit: true);
      } else {
        debugPrint('📷 跳过闪光灯设置（平台不支持）: ${Platform.operatingSystem}');
        _currentFlashMode = FlashMode.off; // 在不支持的平台上默认关闭
      }

      _isInitialized = true;
      debugPrint('✅ FlutterCameraImplementation 初始化成功');
      debugPrint('📷 最终使用的摄像头信息: ${_controller!.description.name}');
      debugPrint('📷 预览尺寸: ${_controller!.value.previewSize}');
      debugPrint('📷 宽高比: ${_controller!.value.aspectRatio}');
    } catch (e, stackTrace) {
      debugPrint('❌ FlutterCameraImplementation 初始化失败: $e');
      debugPrint('📚 堆栈跟踪: $stackTrace');

      // 清理资源
      await _controller?.dispose();
      _controller = null;
      _isInitialized = false;

      throw CameraError(
        code: 'INITIALIZATION_FAILED',
        message: '摄像头初始化失败: ${e.toString()}',
      );
    }
  }

  /// 创建摄像头预览Widget
  static Widget buildPreview({
    required CameraPreviewConfig previewConfig,
    VoidCallback? onCameraReady,
    Function(CameraError)? onError,
  }) {
    if (!_isInitialized || _controller == null) {
      return Container(
        color: Colors.black,
        child: const Center(
          child: Text(
            '摄像头未初始化',
            style: TextStyle(color: Colors.white),
          ),
        ),
      );
    }

    if (!_controller!.value.isInitialized) {
      return Container(
        color: Colors.black,
        child: const Center(
          child: CircularProgressIndicator(color: Colors.white),
        ),
      );
    }

    // 获取相机的宽高比
    final aspectRatio = _controller!.value.aspectRatio;
    debugPrint('📐 FlutterCamera: 相机宽高比 = $aspectRatio');

    Widget preview;

    // 应用尺寸配置
    if (previewConfig.width != null || previewConfig.height != null) {
      final targetWidth = previewConfig.width!;
      final targetHeight = previewConfig.height!;
      debugPrint('📐 FlutterCamera: 目标尺寸 = ${targetWidth}x${targetHeight}');

      final previewSize = _controller!.value.previewSize;
      debugPrint(
          '📐 FlutterCamera: 预览原始(物理)尺寸 = ${previewSize?.width}x${previewSize?.height}');

      if (previewSize == null) {
        // 如果预览尺寸为空，返回一个占位符，避免崩溃
        return Container(
          color: Colors.black,
          child: const Center(
              child: Text('无法获取预览尺寸', style: TextStyle(color: Colors.white))),
        );
      }
      /*
      问题的最终根源
物理与视觉的差异：手机相机传感器的物理形态通常是横向的。camera插件返回的aspectRatio（宽高比）和previewSize（预览像素尺寸）都是基于这个横向的物理传感器。例如，aspectRatio 可能是 1.77 (16:9)，previewSize 可能是 1920x1080。
UI的自动旋转：当我们在竖屏模式下使用App时，CameraPreview这个Widget在内部自动将图像旋转了90度，以便我们能看到正立的画面。
核心矛盾：我们之前所有的尝试，都是在用一个横向的比例（无论是aspectRatio还是previewSize.width/height）去约束一个已经被旋转成纵向的画面。这就像试图把一张横向的照片硬塞进一个竖向的相框，无论怎么缩放，结果必然是错误的拉伸或压缩。
真正“微调”的最终方案：交换尺寸
既然UI把画面旋转了，那我们在布局时，也必须将从插件获取的尺寸手动交换过来，以匹配UI的显示。
之前的所有尝试：使用 width: 1920, height: 1080 来创建容器。
本次的最终方案：使用 width: 1080, height: 1920 来创建一个与UI显示完全匹配的、完美的竖向比例容器。
然后，我们再将这个比例完美的容器交给FittedBox(fit: BoxFit.cover)去缩放，它就能在不产生任何形变的前提下，将其完美地填充到你的正方形/圆形预览区域中。
iOS相机传感器      →  Flutter插件统一处理  →  返回物理尺寸 (1920x1080)
Android相机传感器  →  Flutter插件统一处理  →  返回物理尺寸 (1920x1080)
                                         ↓
                    Flutter UI层都需要交换  →  显示尺寸 (1080x1920)
      */

      preview = SizedBox(
        width: targetWidth,
        height: targetHeight,
        child: ClipRect(
          child: FittedBox(
            fit: BoxFit.cover,
            child: SizedBox(
              // 关键修正：交换宽高以匹配UI的纵向显示
              width: previewSize.height,
              height: previewSize.width,
              child: camera.CameraPreview(_controller!),
            ),
          ),
        ),
      );
    } else {
      // 没有指定尺寸时，使用相机的原始宽高比
      preview = AspectRatio(
        aspectRatio: aspectRatio,
        child: camera.CameraPreview(_controller!),
      );
    }

    if (previewConfig.isCircular) {
      preview = ClipOval(child: preview);
    } else if (previewConfig.borderRadius > 0) {
      preview = ClipRRect(
        borderRadius: BorderRadius.circular(previewConfig.borderRadius),
        child: preview,
      );
    }

    if (previewConfig.showBorder) {
      preview = Container(
        decoration: BoxDecoration(
          border: Border.all(
            color: Color(previewConfig.borderColor ?? 0xFFFFFFFF),
            width: previewConfig.borderWidth,
          ),
          borderRadius: previewConfig.isCircular
              ? null
              : BorderRadius.circular(previewConfig.borderRadius),
          shape:
              previewConfig.isCircular ? BoxShape.circle : BoxShape.rectangle,
        ),
        child: preview,
      );
    }

    // 只设置一次回调，避免重复添加
    if (!_callbacksSetup && _controller!.value.isInitialized) {
      _callbacksSetup = true;
      WidgetsBinding.instance.addPostFrameCallback((_) {
        onCameraReady?.call();
      });
    }

    // 检查错误状态（但不使用addPostFrameCallback避免循环）
    if (_controller!.value.hasError) {
      onError?.call(CameraError(
        code: 'CAMERA_ERROR',
        message: _controller!.value.errorDescription ?? '摄像头错误',
      ));
    }

    return preview;
  }

  /// 拍照
  static Future<CaptureResult> takePicture() async {
    if (!_isInitialized || _controller == null) {
      throw CameraError(
        code: 'NOT_INITIALIZED',
        message: '摄像头未初始化',
      );
    }

    if (!_controller!.value.isInitialized) {
      throw CameraError(
        code: 'CAMERA_NOT_READY',
        message: '摄像头未准备就绪',
      );
    }

    try {
      // 设置图片质量
      if (_config?.imageQuality != null) {
        // Flutter Camera不直接支持图片质量设置，但可以通过其他方式处理
      }

      final camera.XFile image = await _controller!.takePicture();
      final file = File(image.path);
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final fileSize = await file.length();

      return CaptureResult(
        imagePath: image.path,
        timestamp: timestamp,
        fileSize: fileSize,
      );
    } catch (e) {
      throw CameraError(
        code: 'CAPTURE_FAILED',
        message: '拍照失败: ${e.toString()}',
      );
    }
  }

  /// 切换摄像头方向
  static Future<void> switchCamera(CameraLensDirection direction) async {
    if (!_isInitialized || _cameras == null) {
      throw CameraError(
        code: 'NOT_INITIALIZED',
        message: '摄像头未初始化',
      );
    }

    final cameraDesc = _findCamera(direction);
    if (cameraDesc == null) {
      throw CameraError(
        code: 'CAMERA_NOT_FOUND',
        message: '未找到指定方向的摄像头',
      );
    }

    try {
      // 释放当前控制器
      await _controller?.dispose();

      // 创建新的控制器
      _controller = camera.CameraController(
        cameraDesc,
        camera.ResolutionPreset.high,
        enableAudio: _config?.enableAudio ?? false,
        imageFormatGroup: camera.ImageFormatGroup.jpeg,
      );

      // 初始化新控制器
      await _controller!.initialize();

      // 恢复闪光灯设置
      await setFlashMode(_currentFlashMode, duringInit: true);

      _currentDirection = direction;
    } catch (e) {
      throw CameraError(
        code: 'SWITCH_FAILED',
        message: '切换摄像头失败: ${e.toString()}',
      );
    }
  }

  /// 设置闪光灯模式
  static Future<void> setFlashMode(FlashMode mode,
      {bool duringInit = false}) async {
    debugPrint('💡 FlutterCamera: 设置闪光灯模式 $mode (初始化期间: $duringInit)');

    // 如果不是初始化期间，需要检查初始化状态
    if (!duringInit && (!_isInitialized || _controller == null)) {
      debugPrint('❌ FlutterCamera: 摄像头未初始化，跳过闪光灯设置');
      throw CameraError(
        code: 'NOT_INITIALIZED',
        message: '摄像头未初始化',
      );
    }

    // 检查平台支持
    if (!Platform.isIOS && !Platform.isAndroid) {
      debugPrint(
          '💡 FlutterCamera: 平台 ${Platform.operatingSystem} 不支持闪光灯，跳过设置');
      _currentFlashMode = mode; // 仍然保存模式设置
      return;
    }

    try {
      final flashMode = _convertFlashMode(mode);
      debugPrint('💡 FlutterCamera: 转换闪光灯模式: $mode -> $flashMode');

      // 检查控制器状态
      if (_controller == null || !_controller!.value.isInitialized) {
        debugPrint('💡 FlutterCamera: 控制器未初始化，跳过闪光灯设置');
        _currentFlashMode = mode;
        return;
      }

      await _controller!.setFlashMode(flashMode);
      _currentFlashMode = mode;
      debugPrint('✅ FlutterCamera: 闪光灯模式设置成功');
    } catch (e) {
      debugPrint('❌ FlutterCamera: 设置闪光灯模式失败: $e');
      // 对于闪光灯设置失败，我们不抛出异常，而是记录错误并继续
      _currentFlashMode = mode; // 仍然保存模式设置
      debugPrint('⚠️ FlutterCamera: 闪光灯设置失败，但继续初始化');
    }
  }

  /// 开始预览
  static Future<void> startPreview() async {
    // Flutter Camera自动处理预览
  }

  /// 停止预览
  static Future<void> stopPreview() async {
    // Flutter Camera自动处理预览
  }

  /// 释放资源
  static Future<void> dispose() async {
    await _controller?.dispose();
    _controller = null;
    _config = null;
    _cameras = null;
    _isInitialized = false;
    _callbacksSetup = false; // 重置回调标志
  }

  /// 暂停摄像头session
  static Future<void> pauseSession() async {
    if (!_isInitialized || _controller == null) {
      throw CameraError(
        code: 'NOT_INITIALIZED',
        message: '摄像头未初始化',
      );
    }

    try {
      // Flutter Camera通过暂停预览来实现session暂停
      await _controller!.pausePreview();
      debugPrint('📱 FlutterCamera: session已暂停');
    } catch (e) {
      throw CameraError(
        code: 'PAUSE_SESSION_FAILED',
        message: '暂停FlutterCamera session失败: ${e.toString()}',
      );
    }
  }

  /// 恢复摄像头session
  static Future<void> resumeSession() async {
    if (!_isInitialized || _controller == null) {
      throw CameraError(
        code: 'NOT_INITIALIZED',
        message: '摄像头未初始化',
      );
    }

    try {
      // Flutter Camera通过恢复预览来实现session恢复
      await _controller!.resumePreview();
      debugPrint('📱 FlutterCamera: session已恢复');
    } catch (e) {
      throw CameraError(
        code: 'RESUME_SESSION_FAILED',
        message: '恢复FlutterCamera session失败: ${e.toString()}',
      );
    }
  }

  /// 查找指定方向的摄像头
  static camera.CameraDescription? _findCamera(CameraLensDirection direction) {
    if (_cameras == null) return null;

    final lensDirection = _convertLensDirection(direction);
    for (final cameraDesc in _cameras!) {
      if (cameraDesc.lensDirection == lensDirection) {
        return cameraDesc;
      }
    }
    return null;
  }

  /// 转换摄像头方向
  static camera.CameraLensDirection _convertLensDirection(
      CameraLensDirection direction) {
    switch (direction) {
      case CameraLensDirection.front:
        return camera.CameraLensDirection.front;
      case CameraLensDirection.back:
        return camera.CameraLensDirection.back;
      case CameraLensDirection.external:
        return camera.CameraLensDirection.external;
    }
  }

  /// 转换闪光灯模式
  static camera.FlashMode _convertFlashMode(FlashMode mode) {
    switch (mode) {
      case FlashMode.off:
        return camera.FlashMode.off;
      case FlashMode.on:
        return camera.FlashMode.always;
      case FlashMode.auto:
        return camera.FlashMode.auto;
      case FlashMode.torch:
        return camera.FlashMode.torch;
    }
  }

  /// 获取当前摄像头方向
  static CameraLensDirection get currentDirection => _currentDirection;

  /// 获取当前闪光灯模式
  static FlashMode get currentFlashMode => _currentFlashMode;

  /// 是否已初始化
  static bool get isInitialized => _isInitialized;

  /// 获取摄像头控制器（用于高级功能）
  static camera.CameraController? get controller => _controller;
}
