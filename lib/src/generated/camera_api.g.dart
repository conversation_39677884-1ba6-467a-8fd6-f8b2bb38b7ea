// Autogenerated from <PERSON><PERSON> (v22.7.2), do not edit directly.
// See also: https://pub.dev/packages/pigeon
// ignore_for_file: public_member_api_docs, non_constant_identifier_names, avoid_as, unused_import, unnecessary_parenthesis, prefer_null_aware_operators, omit_local_variable_types, unused_shown_name, unnecessary_import, no_leading_underscores_for_local_identifiers

import 'dart:async';
import 'dart:typed_data' show Float64List, Int32List, Int64List, Uint8List;

import 'package:flutter/foundation.dart' show ReadBuffer, WriteBuffer;
import 'package:flutter/services.dart';

PlatformException _createConnectionError(String channelName) {
  return PlatformException(
    code: 'channel-error',
    message: 'Unable to establish connection on channel: "$channelName".',
  );
}

List<Object?> wrapResponse({Object? result, PlatformException? error, bool empty = false}) {
  if (empty) {
    return <Object?>[];
  }
  if (error == null) {
    return <Object?>[result];
  }
  return <Object?>[error.code, error.message, error.details];
}

/// 摄像头实现类型枚举
enum CameraImplementationType {
  /// Flutter Camera插件实现
  flutterCamera,
  /// 原生实现
  native,
}

/// 摄像头状态枚举
enum CameraStatus {
  /// 未初始化
  uninitialized,
  /// 初始化中
  initializing,
  /// 已初始化
  initialized,
  /// 拍照中
  takingPicture,
  /// session已暂停
  sessionPaused,
  /// 错误状态
  error,
  /// 已释放
  disposed,
}

/// 摄像头方向枚举
enum CameraLensDirection {
  /// 前置摄像头
  front,
  /// 后置摄像头
  back,
  /// 外部摄像头
  external,
}

/// 闪光灯模式枚举
enum FlashMode {
  /// 关闭
  off,
  /// 开启
  on,
  /// 自动
  auto,
  /// 常亮
  torch,
}

/// 摄像头配置
class CameraConfig {
  CameraConfig({
    required this.lensDirection,
    required this.enableAudio,
    required this.imageQuality,
    this.width,
    this.height,
    required this.isCircular,
    required this.isSquare,
    required this.enableAutoInitialization,
  });

  /// 摄像头方向
  CameraLensDirection lensDirection;

  /// 是否启用音频
  bool enableAudio;

  /// 图片质量 (0.0-1.0)
  double imageQuality;

  /// 摄像头窗口宽度
  double? width;

  /// 摄像头窗口高度
  double? height;

  /// 是否为圆形窗口
  bool isCircular;

  /// 是否为正方形窗口
  bool isSquare;

  /// 是否启用自动初始化
  bool enableAutoInitialization;

  Object encode() {
    return <Object?>[
      lensDirection,
      enableAudio,
      imageQuality,
      width,
      height,
      isCircular,
      isSquare,
      enableAutoInitialization,
    ];
  }

  static CameraConfig decode(Object result) {
    result as List<Object?>;
    return CameraConfig(
      lensDirection: result[0]! as CameraLensDirection,
      enableAudio: result[1]! as bool,
      imageQuality: result[2]! as double,
      width: result[3] as double?,
      height: result[4] as double?,
      isCircular: result[5]! as bool,
      isSquare: result[6]! as bool,
      enableAutoInitialization: result[7]! as bool,
    );
  }
}

/// 拍照结果
class CaptureResult {
  CaptureResult({
    required this.imagePath,
    required this.timestamp,
    required this.fileSize,
  });

  /// 图片文件路径
  String imagePath;

  /// 拍照时间戳
  int timestamp;

  /// 图片大小（字节）
  int fileSize;

  Object encode() {
    return <Object?>[
      imagePath,
      timestamp,
      fileSize,
    ];
  }

  static CaptureResult decode(Object result) {
    result as List<Object?>;
    return CaptureResult(
      imagePath: result[0]! as String,
      timestamp: result[1]! as int,
      fileSize: result[2]! as int,
    );
  }
}

/// 摄像头错误信息
class CameraError {
  CameraError({
    required this.code,
    required this.message,
    this.details,
  });

  /// 错误代码
  String code;

  /// 错误消息
  String message;

  /// 错误详情
  String? details;

  Object encode() {
    return <Object?>[
      code,
      message,
      details,
    ];
  }

  static CameraError decode(Object result) {
    result as List<Object?>;
    return CameraError(
      code: result[0]! as String,
      message: result[1]! as String,
      details: result[2] as String?,
    );
  }
}

/// 摄像头能力信息
class CameraCapabilities {
  CameraCapabilities({
    required this.supportedImplementations,
    required this.hasFrontCamera,
    required this.hasBackCamera,
    required this.hasFlash,
    required this.hasZoom,
  });

  /// 支持的实现类型列表
  List<CameraImplementationType> supportedImplementations;

  /// 是否支持前置摄像头
  bool hasFrontCamera;

  /// 是否支持后置摄像头
  bool hasBackCamera;

  /// 是否支持闪光灯
  bool hasFlash;

  /// 是否支持变焦
  bool hasZoom;

  Object encode() {
    return <Object?>[
      supportedImplementations,
      hasFrontCamera,
      hasBackCamera,
      hasFlash,
      hasZoom,
    ];
  }

  static CameraCapabilities decode(Object result) {
    result as List<Object?>;
    return CameraCapabilities(
      supportedImplementations: (result[0] as List<Object?>?)!.cast<CameraImplementationType>(),
      hasFrontCamera: result[1]! as bool,
      hasBackCamera: result[2]! as bool,
      hasFlash: result[3]! as bool,
      hasZoom: result[4]! as bool,
    );
  }
}

class CameraTexture {
  CameraTexture({
    this.textureId,
    this.width,
    this.height,
  });

  int? textureId;

  double? width;

  double? height;

  Object encode() {
    return <Object?>[
      textureId,
      width,
      height,
    ];
  }

  static CameraTexture decode(Object result) {
    result as List<Object?>;
    return CameraTexture(
      textureId: result[0] as int?,
      width: result[1] as double?,
      height: result[2] as double?,
    );
  }
}


class _PigeonCodec extends StandardMessageCodec {
  const _PigeonCodec();
  @override
  void writeValue(WriteBuffer buffer, Object? value) {
    if (value is int) {
      buffer.putUint8(4);
      buffer.putInt64(value);
    }    else if (value is CameraImplementationType) {
      buffer.putUint8(129);
      writeValue(buffer, value.index);
    }    else if (value is CameraStatus) {
      buffer.putUint8(130);
      writeValue(buffer, value.index);
    }    else if (value is CameraLensDirection) {
      buffer.putUint8(131);
      writeValue(buffer, value.index);
    }    else if (value is FlashMode) {
      buffer.putUint8(132);
      writeValue(buffer, value.index);
    }    else if (value is CameraConfig) {
      buffer.putUint8(133);
      writeValue(buffer, value.encode());
    }    else if (value is CaptureResult) {
      buffer.putUint8(134);
      writeValue(buffer, value.encode());
    }    else if (value is CameraError) {
      buffer.putUint8(135);
      writeValue(buffer, value.encode());
    }    else if (value is CameraCapabilities) {
      buffer.putUint8(136);
      writeValue(buffer, value.encode());
    }    else if (value is CameraTexture) {
      buffer.putUint8(137);
      writeValue(buffer, value.encode());
    } else {
      super.writeValue(buffer, value);
    }
  }

  @override
  Object? readValueOfType(int type, ReadBuffer buffer) {
    switch (type) {
      case 129: 
        final int? value = readValue(buffer) as int?;
        return value == null ? null : CameraImplementationType.values[value];
      case 130: 
        final int? value = readValue(buffer) as int?;
        return value == null ? null : CameraStatus.values[value];
      case 131: 
        final int? value = readValue(buffer) as int?;
        return value == null ? null : CameraLensDirection.values[value];
      case 132: 
        final int? value = readValue(buffer) as int?;
        return value == null ? null : FlashMode.values[value];
      case 133: 
        return CameraConfig.decode(readValue(buffer)!);
      case 134: 
        return CaptureResult.decode(readValue(buffer)!);
      case 135: 
        return CameraError.decode(readValue(buffer)!);
      case 136: 
        return CameraCapabilities.decode(readValue(buffer)!);
      case 137: 
        return CameraTexture.decode(readValue(buffer)!);
      default:
        return super.readValueOfType(type, buffer);
    }
  }
}

/// 主机端API接口（Flutter调用原生）
class MultiCameraHostApi {
  /// Constructor for [MultiCameraHostApi].  The [binaryMessenger] named argument is
  /// available for dependency injection.  If it is left null, the default
  /// BinaryMessenger will be used which routes to the host platform.
  MultiCameraHostApi({BinaryMessenger? binaryMessenger, String messageChannelSuffix = ''})
      : pigeonVar_binaryMessenger = binaryMessenger,
        pigeonVar_messageChannelSuffix = messageChannelSuffix.isNotEmpty ? '.$messageChannelSuffix' : '';
  final BinaryMessenger? pigeonVar_binaryMessenger;

  static const MessageCodec<Object?> pigeonChannelCodec = _PigeonCodec();

  final String pigeonVar_messageChannelSuffix;

  /// 获取摄像头能力
  Future<CameraCapabilities> getCameraCapabilities() async {
    final String pigeonVar_channelName = 'dev.flutter.pigeon.multi_camera_plugin.MultiCameraHostApi.getCameraCapabilities$pigeonVar_messageChannelSuffix';
    final BasicMessageChannel<Object?> pigeonVar_channel = BasicMessageChannel<Object?>(
      pigeonVar_channelName,
      pigeonChannelCodec,
      binaryMessenger: pigeonVar_binaryMessenger,
    );
    final List<Object?>? pigeonVar_replyList =
        await pigeonVar_channel.send(null) as List<Object?>?;
    if (pigeonVar_replyList == null) {
      throw _createConnectionError(pigeonVar_channelName);
    } else if (pigeonVar_replyList.length > 1) {
      throw PlatformException(
        code: pigeonVar_replyList[0]! as String,
        message: pigeonVar_replyList[1] as String?,
        details: pigeonVar_replyList[2],
      );
    } else if (pigeonVar_replyList[0] == null) {
      throw PlatformException(
        code: 'null-error',
        message: 'Host platform returned null value for non-null return value.',
      );
    } else {
      return (pigeonVar_replyList[0] as CameraCapabilities?)!;
    }
  }

  /// 初始化摄像头
  Future<void> initializeCamera(CameraConfig config, CameraImplementationType implementationType) async {
    final String pigeonVar_channelName = 'dev.flutter.pigeon.multi_camera_plugin.MultiCameraHostApi.initializeCamera$pigeonVar_messageChannelSuffix';
    final BasicMessageChannel<Object?> pigeonVar_channel = BasicMessageChannel<Object?>(
      pigeonVar_channelName,
      pigeonChannelCodec,
      binaryMessenger: pigeonVar_binaryMessenger,
    );
    final List<Object?>? pigeonVar_replyList =
        await pigeonVar_channel.send(<Object?>[config, implementationType]) as List<Object?>?;
    if (pigeonVar_replyList == null) {
      throw _createConnectionError(pigeonVar_channelName);
    } else if (pigeonVar_replyList.length > 1) {
      throw PlatformException(
        code: pigeonVar_replyList[0]! as String,
        message: pigeonVar_replyList[1] as String?,
        details: pigeonVar_replyList[2],
      );
    } else {
      return;
    }
  }

  /// 切换实现方式
  Future<void> switchImplementation(CameraImplementationType implementationType) async {
    final String pigeonVar_channelName = 'dev.flutter.pigeon.multi_camera_plugin.MultiCameraHostApi.switchImplementation$pigeonVar_messageChannelSuffix';
    final BasicMessageChannel<Object?> pigeonVar_channel = BasicMessageChannel<Object?>(
      pigeonVar_channelName,
      pigeonChannelCodec,
      binaryMessenger: pigeonVar_binaryMessenger,
    );
    final List<Object?>? pigeonVar_replyList =
        await pigeonVar_channel.send(<Object?>[implementationType]) as List<Object?>?;
    if (pigeonVar_replyList == null) {
      throw _createConnectionError(pigeonVar_channelName);
    } else if (pigeonVar_replyList.length > 1) {
      throw PlatformException(
        code: pigeonVar_replyList[0]! as String,
        message: pigeonVar_replyList[1] as String?,
        details: pigeonVar_replyList[2],
      );
    } else {
      return;
    }
  }

  /// 切换摄像头方向（前置/后置）
  Future<void> switchCamera(CameraLensDirection direction) async {
    final String pigeonVar_channelName = 'dev.flutter.pigeon.multi_camera_plugin.MultiCameraHostApi.switchCamera$pigeonVar_messageChannelSuffix';
    final BasicMessageChannel<Object?> pigeonVar_channel = BasicMessageChannel<Object?>(
      pigeonVar_channelName,
      pigeonChannelCodec,
      binaryMessenger: pigeonVar_binaryMessenger,
    );
    final List<Object?>? pigeonVar_replyList =
        await pigeonVar_channel.send(<Object?>[direction]) as List<Object?>?;
    if (pigeonVar_replyList == null) {
      throw _createConnectionError(pigeonVar_channelName);
    } else if (pigeonVar_replyList.length > 1) {
      throw PlatformException(
        code: pigeonVar_replyList[0]! as String,
        message: pigeonVar_replyList[1] as String?,
        details: pigeonVar_replyList[2],
      );
    } else {
      return;
    }
  }

  /// 拍照
  Future<CaptureResult> takePicture() async {
    final String pigeonVar_channelName = 'dev.flutter.pigeon.multi_camera_plugin.MultiCameraHostApi.takePicture$pigeonVar_messageChannelSuffix';
    final BasicMessageChannel<Object?> pigeonVar_channel = BasicMessageChannel<Object?>(
      pigeonVar_channelName,
      pigeonChannelCodec,
      binaryMessenger: pigeonVar_binaryMessenger,
    );
    final List<Object?>? pigeonVar_replyList =
        await pigeonVar_channel.send(null) as List<Object?>?;
    if (pigeonVar_replyList == null) {
      throw _createConnectionError(pigeonVar_channelName);
    } else if (pigeonVar_replyList.length > 1) {
      throw PlatformException(
        code: pigeonVar_replyList[0]! as String,
        message: pigeonVar_replyList[1] as String?,
        details: pigeonVar_replyList[2],
      );
    } else if (pigeonVar_replyList[0] == null) {
      throw PlatformException(
        code: 'null-error',
        message: 'Host platform returned null value for non-null return value.',
      );
    } else {
      return (pigeonVar_replyList[0] as CaptureResult?)!;
    }
  }

  /// 设置闪光灯模式
  Future<void> setFlashMode(FlashMode mode) async {
    final String pigeonVar_channelName = 'dev.flutter.pigeon.multi_camera_plugin.MultiCameraHostApi.setFlashMode$pigeonVar_messageChannelSuffix';
    final BasicMessageChannel<Object?> pigeonVar_channel = BasicMessageChannel<Object?>(
      pigeonVar_channelName,
      pigeonChannelCodec,
      binaryMessenger: pigeonVar_binaryMessenger,
    );
    final List<Object?>? pigeonVar_replyList =
        await pigeonVar_channel.send(<Object?>[mode]) as List<Object?>?;
    if (pigeonVar_replyList == null) {
      throw _createConnectionError(pigeonVar_channelName);
    } else if (pigeonVar_replyList.length > 1) {
      throw PlatformException(
        code: pigeonVar_replyList[0]! as String,
        message: pigeonVar_replyList[1] as String?,
        details: pigeonVar_replyList[2],
      );
    } else {
      return;
    }
  }

  /// 开始预览
  Future<void> startPreview() async {
    final String pigeonVar_channelName = 'dev.flutter.pigeon.multi_camera_plugin.MultiCameraHostApi.startPreview$pigeonVar_messageChannelSuffix';
    final BasicMessageChannel<Object?> pigeonVar_channel = BasicMessageChannel<Object?>(
      pigeonVar_channelName,
      pigeonChannelCodec,
      binaryMessenger: pigeonVar_binaryMessenger,
    );
    final List<Object?>? pigeonVar_replyList =
        await pigeonVar_channel.send(null) as List<Object?>?;
    if (pigeonVar_replyList == null) {
      throw _createConnectionError(pigeonVar_channelName);
    } else if (pigeonVar_replyList.length > 1) {
      throw PlatformException(
        code: pigeonVar_replyList[0]! as String,
        message: pigeonVar_replyList[1] as String?,
        details: pigeonVar_replyList[2],
      );
    } else {
      return;
    }
  }

  /// 停止预览
  Future<void> stopPreview() async {
    final String pigeonVar_channelName = 'dev.flutter.pigeon.multi_camera_plugin.MultiCameraHostApi.stopPreview$pigeonVar_messageChannelSuffix';
    final BasicMessageChannel<Object?> pigeonVar_channel = BasicMessageChannel<Object?>(
      pigeonVar_channelName,
      pigeonChannelCodec,
      binaryMessenger: pigeonVar_binaryMessenger,
    );
    final List<Object?>? pigeonVar_replyList =
        await pigeonVar_channel.send(null) as List<Object?>?;
    if (pigeonVar_replyList == null) {
      throw _createConnectionError(pigeonVar_channelName);
    } else if (pigeonVar_replyList.length > 1) {
      throw PlatformException(
        code: pigeonVar_replyList[0]! as String,
        message: pigeonVar_replyList[1] as String?,
        details: pigeonVar_replyList[2],
      );
    } else {
      return;
    }
  }

  /// 释放摄像头资源
  Future<void> dispose() async {
    final String pigeonVar_channelName = 'dev.flutter.pigeon.multi_camera_plugin.MultiCameraHostApi.dispose$pigeonVar_messageChannelSuffix';
    final BasicMessageChannel<Object?> pigeonVar_channel = BasicMessageChannel<Object?>(
      pigeonVar_channelName,
      pigeonChannelCodec,
      binaryMessenger: pigeonVar_binaryMessenger,
    );
    final List<Object?>? pigeonVar_replyList =
        await pigeonVar_channel.send(null) as List<Object?>?;
    if (pigeonVar_replyList == null) {
      throw _createConnectionError(pigeonVar_channelName);
    } else if (pigeonVar_replyList.length > 1) {
      throw PlatformException(
        code: pigeonVar_replyList[0]! as String,
        message: pigeonVar_replyList[1] as String?,
        details: pigeonVar_replyList[2],
      );
    } else {
      return;
    }
  }

  /// 检查特定实现是否可用
  Future<bool> isImplementationAvailable(CameraImplementationType implementationType) async {
    final String pigeonVar_channelName = 'dev.flutter.pigeon.multi_camera_plugin.MultiCameraHostApi.isImplementationAvailable$pigeonVar_messageChannelSuffix';
    final BasicMessageChannel<Object?> pigeonVar_channel = BasicMessageChannel<Object?>(
      pigeonVar_channelName,
      pigeonChannelCodec,
      binaryMessenger: pigeonVar_binaryMessenger,
    );
    final List<Object?>? pigeonVar_replyList =
        await pigeonVar_channel.send(<Object?>[implementationType]) as List<Object?>?;
    if (pigeonVar_replyList == null) {
      throw _createConnectionError(pigeonVar_channelName);
    } else if (pigeonVar_replyList.length > 1) {
      throw PlatformException(
        code: pigeonVar_replyList[0]! as String,
        message: pigeonVar_replyList[1] as String?,
        details: pigeonVar_replyList[2],
      );
    } else if (pigeonVar_replyList[0] == null) {
      throw PlatformException(
        code: 'null-error',
        message: 'Host platform returned null value for non-null return value.',
      );
    } else {
      return (pigeonVar_replyList[0] as bool?)!;
    }
  }

  /// Creates a texture for rendering the camera preview and returns its ID.
  Future<CameraTexture> createCameraTexture() async {
    final String pigeonVar_channelName = 'dev.flutter.pigeon.multi_camera_plugin.MultiCameraHostApi.createCameraTexture$pigeonVar_messageChannelSuffix';
    final BasicMessageChannel<Object?> pigeonVar_channel = BasicMessageChannel<Object?>(
      pigeonVar_channelName,
      pigeonChannelCodec,
      binaryMessenger: pigeonVar_binaryMessenger,
    );
    final List<Object?>? pigeonVar_replyList =
        await pigeonVar_channel.send(null) as List<Object?>?;
    if (pigeonVar_replyList == null) {
      throw _createConnectionError(pigeonVar_channelName);
    } else if (pigeonVar_replyList.length > 1) {
      throw PlatformException(
        code: pigeonVar_replyList[0]! as String,
        message: pigeonVar_replyList[1] as String?,
        details: pigeonVar_replyList[2],
      );
    } else if (pigeonVar_replyList[0] == null) {
      throw PlatformException(
        code: 'null-error',
        message: 'Host platform returned null value for non-null return value.',
      );
    } else {
      return (pigeonVar_replyList[0] as CameraTexture?)!;
    }
  }

  /// 暂停摄像头session
  Future<void> pauseSession() async {
    final String pigeonVar_channelName = 'dev.flutter.pigeon.multi_camera_plugin.MultiCameraHostApi.pauseSession$pigeonVar_messageChannelSuffix';
    final BasicMessageChannel<Object?> pigeonVar_channel = BasicMessageChannel<Object?>(
      pigeonVar_channelName,
      pigeonChannelCodec,
      binaryMessenger: pigeonVar_binaryMessenger,
    );
    final List<Object?>? pigeonVar_replyList =
        await pigeonVar_channel.send(null) as List<Object?>?;
    if (pigeonVar_replyList == null) {
      throw _createConnectionError(pigeonVar_channelName);
    } else if (pigeonVar_replyList.length > 1) {
      throw PlatformException(
        code: pigeonVar_replyList[0]! as String,
        message: pigeonVar_replyList[1] as String?,
        details: pigeonVar_replyList[2],
      );
    } else {
      return;
    }
  }

  /// 恢复摄像头session
  Future<void> resumeSession() async {
    final String pigeonVar_channelName = 'dev.flutter.pigeon.multi_camera_plugin.MultiCameraHostApi.resumeSession$pigeonVar_messageChannelSuffix';
    final BasicMessageChannel<Object?> pigeonVar_channel = BasicMessageChannel<Object?>(
      pigeonVar_channelName,
      pigeonChannelCodec,
      binaryMessenger: pigeonVar_binaryMessenger,
    );
    final List<Object?>? pigeonVar_replyList =
        await pigeonVar_channel.send(null) as List<Object?>?;
    if (pigeonVar_replyList == null) {
      throw _createConnectionError(pigeonVar_channelName);
    } else if (pigeonVar_replyList.length > 1) {
      throw PlatformException(
        code: pigeonVar_replyList[0]! as String,
        message: pigeonVar_replyList[1] as String?,
        details: pigeonVar_replyList[2],
      );
    } else {
      return;
    }
  }

  /// 检查session是否已暂停
  Future<bool> isSessionPaused() async {
    final String pigeonVar_channelName = 'dev.flutter.pigeon.multi_camera_plugin.MultiCameraHostApi.isSessionPaused$pigeonVar_messageChannelSuffix';
    final BasicMessageChannel<Object?> pigeonVar_channel = BasicMessageChannel<Object?>(
      pigeonVar_channelName,
      pigeonChannelCodec,
      binaryMessenger: pigeonVar_binaryMessenger,
    );
    final List<Object?>? pigeonVar_replyList =
        await pigeonVar_channel.send(null) as List<Object?>?;
    if (pigeonVar_replyList == null) {
      throw _createConnectionError(pigeonVar_channelName);
    } else if (pigeonVar_replyList.length > 1) {
      throw PlatformException(
        code: pigeonVar_replyList[0]! as String,
        message: pigeonVar_replyList[1] as String?,
        details: pigeonVar_replyList[2],
      );
    } else if (pigeonVar_replyList[0] == null) {
      throw PlatformException(
        code: 'null-error',
        message: 'Host platform returned null value for non-null return value.',
      );
    } else {
      return (pigeonVar_replyList[0] as bool?)!;
    }
  }

  /// 原生权限检测（iOS/Android原生方法）
  Future<bool> checkCameraPermissionNative() async {
    final String pigeonVar_channelName = 'dev.flutter.pigeon.multi_camera_plugin.MultiCameraHostApi.checkCameraPermissionNative$pigeonVar_messageChannelSuffix';
    final BasicMessageChannel<Object?> pigeonVar_channel = BasicMessageChannel<Object?>(
      pigeonVar_channelName,
      pigeonChannelCodec,
      binaryMessenger: pigeonVar_binaryMessenger,
    );
    final List<Object?>? pigeonVar_replyList =
        await pigeonVar_channel.send(null) as List<Object?>?;
    if (pigeonVar_replyList == null) {
      throw _createConnectionError(pigeonVar_channelName);
    } else if (pigeonVar_replyList.length > 1) {
      throw PlatformException(
        code: pigeonVar_replyList[0]! as String,
        message: pigeonVar_replyList[1] as String?,
        details: pigeonVar_replyList[2],
      );
    } else if (pigeonVar_replyList[0] == null) {
      throw PlatformException(
        code: 'null-error',
        message: 'Host platform returned null value for non-null return value.',
      );
    } else {
      return (pigeonVar_replyList[0] as bool?)!;
    }
  }
}

/// Flutter端API接口（原生调用Flutter）
abstract class MultiCameraFlutterApi {
  static const MessageCodec<Object?> pigeonChannelCodec = _PigeonCodec();

  /// 摄像头状态变化回调
  void onCameraStatusChanged(CameraStatus status);

  /// 摄像头错误回调
  void onCameraError(CameraError error);

  /// 实现方案自动切换回调
  void onImplementationSwitched(CameraImplementationType newImplementation, String reason);

  static void setUp(MultiCameraFlutterApi? api, {BinaryMessenger? binaryMessenger, String messageChannelSuffix = '',}) {
    messageChannelSuffix = messageChannelSuffix.isNotEmpty ? '.$messageChannelSuffix' : '';
    {
      final BasicMessageChannel<Object?> pigeonVar_channel = BasicMessageChannel<Object?>(
          'dev.flutter.pigeon.multi_camera_plugin.MultiCameraFlutterApi.onCameraStatusChanged$messageChannelSuffix', pigeonChannelCodec,
          binaryMessenger: binaryMessenger);
      if (api == null) {
        pigeonVar_channel.setMessageHandler(null);
      } else {
        pigeonVar_channel.setMessageHandler((Object? message) async {
          assert(message != null,
          'Argument for dev.flutter.pigeon.multi_camera_plugin.MultiCameraFlutterApi.onCameraStatusChanged was null.');
          final List<Object?> args = (message as List<Object?>?)!;
          final CameraStatus? arg_status = (args[0] as CameraStatus?);
          assert(arg_status != null,
              'Argument for dev.flutter.pigeon.multi_camera_plugin.MultiCameraFlutterApi.onCameraStatusChanged was null, expected non-null CameraStatus.');
          try {
            api.onCameraStatusChanged(arg_status!);
            return wrapResponse(empty: true);
          } on PlatformException catch (e) {
            return wrapResponse(error: e);
          }          catch (e) {
            return wrapResponse(error: PlatformException(code: 'error', message: e.toString()));
          }
        });
      }
    }
    {
      final BasicMessageChannel<Object?> pigeonVar_channel = BasicMessageChannel<Object?>(
          'dev.flutter.pigeon.multi_camera_plugin.MultiCameraFlutterApi.onCameraError$messageChannelSuffix', pigeonChannelCodec,
          binaryMessenger: binaryMessenger);
      if (api == null) {
        pigeonVar_channel.setMessageHandler(null);
      } else {
        pigeonVar_channel.setMessageHandler((Object? message) async {
          assert(message != null,
          'Argument for dev.flutter.pigeon.multi_camera_plugin.MultiCameraFlutterApi.onCameraError was null.');
          final List<Object?> args = (message as List<Object?>?)!;
          final CameraError? arg_error = (args[0] as CameraError?);
          assert(arg_error != null,
              'Argument for dev.flutter.pigeon.multi_camera_plugin.MultiCameraFlutterApi.onCameraError was null, expected non-null CameraError.');
          try {
            api.onCameraError(arg_error!);
            return wrapResponse(empty: true);
          } on PlatformException catch (e) {
            return wrapResponse(error: e);
          }          catch (e) {
            return wrapResponse(error: PlatformException(code: 'error', message: e.toString()));
          }
        });
      }
    }
    {
      final BasicMessageChannel<Object?> pigeonVar_channel = BasicMessageChannel<Object?>(
          'dev.flutter.pigeon.multi_camera_plugin.MultiCameraFlutterApi.onImplementationSwitched$messageChannelSuffix', pigeonChannelCodec,
          binaryMessenger: binaryMessenger);
      if (api == null) {
        pigeonVar_channel.setMessageHandler(null);
      } else {
        pigeonVar_channel.setMessageHandler((Object? message) async {
          assert(message != null,
          'Argument for dev.flutter.pigeon.multi_camera_plugin.MultiCameraFlutterApi.onImplementationSwitched was null.');
          final List<Object?> args = (message as List<Object?>?)!;
          final CameraImplementationType? arg_newImplementation = (args[0] as CameraImplementationType?);
          assert(arg_newImplementation != null,
              'Argument for dev.flutter.pigeon.multi_camera_plugin.MultiCameraFlutterApi.onImplementationSwitched was null, expected non-null CameraImplementationType.');
          final String? arg_reason = (args[1] as String?);
          assert(arg_reason != null,
              'Argument for dev.flutter.pigeon.multi_camera_plugin.MultiCameraFlutterApi.onImplementationSwitched was null, expected non-null String.');
          try {
            api.onImplementationSwitched(arg_newImplementation!, arg_reason!);
            return wrapResponse(empty: true);
          } on PlatformException catch (e) {
            return wrapResponse(error: e);
          }          catch (e) {
            return wrapResponse(error: PlatformException(code: 'error', message: e.toString()));
          }
        });
      }
    }
  }
}
