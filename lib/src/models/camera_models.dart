/// 多摄像头插件的数据模型定义
library;

// 导入Pigeon生成的API类
import '../generated/camera_api.g.dart';

// 导出Pigeon生成的API类
export '../generated/camera_api.g.dart';

/// 摄像头预览配置
class CameraPreviewConfig {
  /// 预览窗口宽度
  final double? width;

  /// 预览窗口高度
  final double? height;

  /// 是否为圆形预览窗口
  final bool isCircular;

  /// 是否为正方形预览窗口
  final bool isSquare;

  /// 预览窗口圆角半径
  final double borderRadius;

  /// 是否显示预览边框
  final bool showBorder;

  /// 预览边框颜色
  final int? borderColor;

  /// 预览边框宽度
  final double borderWidth;

  const CameraPreviewConfig({
    this.width,
    this.height,
    this.isCircular = false,
    this.isSquare = false,
    this.borderRadius = 0.0,
    this.showBorder = false,
    this.borderColor,
    this.borderWidth = 1.0,
  });

  /// 创建圆形预览配置
  factory CameraPreviewConfig.circular({
    required double size,
    bool showBorder = false,
    int? borderColor,
    double borderWidth = 1.0,
  }) {
    return CameraPreviewConfig(
      width: size,
      height: size,
      isCircular: true,
      showBorder: showBorder,
      borderColor: borderColor,
      borderWidth: borderWidth,
    );
  }

  /// 创建正方形预览配置
  factory CameraPreviewConfig.square({
    required double size,
    double borderRadius = 0.0,
    bool showBorder = false,
    int? borderColor,
    double borderWidth = 1.0,
  }) {
    return CameraPreviewConfig(
      width: size,
      height: size,
      isSquare: true,
      borderRadius: borderRadius,
      showBorder: showBorder,
      borderColor: borderColor,
      borderWidth: borderWidth,
    );
  }

  /// 创建矩形预览配置
  factory CameraPreviewConfig.rectangle({
    required double width,
    required double height,
    double borderRadius = 0.0,
    bool showBorder = false,
    int? borderColor,
    double borderWidth = 1.0,
  }) {
    return CameraPreviewConfig(
      width: width,
      height: height,
      borderRadius: borderRadius,
      showBorder: showBorder,
      borderColor: borderColor,
      borderWidth: borderWidth,
    );
  }

  CameraPreviewConfig copyWith({
    double? width,
    double? height,
    bool? isCircular,
    bool? isSquare,
    double? borderRadius,
    bool? showBorder,
    int? borderColor,
    double? borderWidth,
  }) {
    return CameraPreviewConfig(
      width: width ?? this.width,
      height: height ?? this.height,
      isCircular: isCircular ?? this.isCircular,
      isSquare: isSquare ?? this.isSquare,
      borderRadius: borderRadius ?? this.borderRadius,
      showBorder: showBorder ?? this.showBorder,
      borderColor: borderColor ?? this.borderColor,
      borderWidth: borderWidth ?? this.borderWidth,
    );
  }
}

/// 摄像头实现优先级配置
class CameraImplementationPriority {
  /// iOS平台的实现优先级列表
  final List<CameraImplementationType> iosPriority;

  /// Android平台的实现优先级列表
  final List<CameraImplementationType> androidPriority;

  /// 是否启用自动降级
  final bool enableAutoFallback;

  /// 自动降级的最大重试次数
  final int maxRetryCount;

  const CameraImplementationPriority({
    this.iosPriority = const [
      CameraImplementationType.native,
      CameraImplementationType.flutterCamera,
    ],
    this.androidPriority = const [
      CameraImplementationType.flutterCamera,
      CameraImplementationType.native,
    ],
    this.enableAutoFallback = true,
    this.maxRetryCount = 3,
  });
}

/// 摄像头事件
abstract class CameraEvent {
  const CameraEvent();
}

/// 摄像头状态变化事件
class CameraStatusChangedEvent extends CameraEvent {
  final CameraStatus status;
  final CameraImplementationType? implementationType;

  const CameraStatusChangedEvent(this.status, [this.implementationType]);
}

/// 摄像头错误事件
class CameraErrorEvent extends CameraEvent {
  final CameraError error;
  final CameraImplementationType? implementationType;

  const CameraErrorEvent(this.error, [this.implementationType]);
}

/// 摄像头实现切换事件
class CameraImplementationSwitchedEvent extends CameraEvent {
  final CameraImplementationType oldImplementation;
  final CameraImplementationType newImplementation;
  final String reason;

  const CameraImplementationSwitchedEvent(
    this.oldImplementation,
    this.newImplementation,
    this.reason,
  );
}

/// 拍照完成事件
class CameraCaptureCompletedEvent extends CameraEvent {
  final CaptureResult result;
  final CameraImplementationType implementationType;

  const CameraCaptureCompletedEvent(this.result, this.implementationType);
}

/// 摄像头方向切换事件
class CameraDirectionSwitchedEvent extends CameraEvent {
  final CameraLensDirection oldDirection;
  final CameraLensDirection newDirection;

  const CameraDirectionSwitchedEvent(this.oldDirection, this.newDirection);
}

/// 闪光灯模式变化事件
class FlashModeChangedEvent extends CameraEvent {
  final FlashMode oldMode;
  final FlashMode newMode;

  const FlashModeChangedEvent(this.oldMode, this.newMode);
}

/// 自动初始化开始事件
class AutoInitializationStartedEvent extends CameraEvent {
  final CameraConfig config;

  const AutoInitializationStartedEvent(this.config);
}

/// 自动初始化完成事件
class AutoInitializationCompletedEvent extends CameraEvent {
  final CameraImplementationType implementationType;
  final bool success;
  final String? errorMessage;

  const AutoInitializationCompletedEvent(
    this.implementationType,
    this.success, [
    this.errorMessage,
  ]);
}

/// Session暂停事件
class CameraSessionPausedEvent extends CameraEvent {
  final CameraImplementationType implementationType;
  final String reason;

  const CameraSessionPausedEvent(this.implementationType, this.reason);
}

/// Session恢复事件
class CameraSessionResumedEvent extends CameraEvent {
  final CameraImplementationType implementationType;

  const CameraSessionResumedEvent(this.implementationType);
}

/// CameraConfig扩展方法
extension CameraConfigExtension on CameraConfig {
  /// 创建CameraConfig的副本，允许修改部分属性
  CameraConfig copyWith({
    CameraLensDirection? lensDirection,
    bool? enableAudio,
    double? imageQuality,
    double? width,
    double? height,
    bool? isCircular,
    bool? isSquare,
    bool? enableAutoInitialization,
  }) {
    return CameraConfig(
      lensDirection: lensDirection ?? this.lensDirection,
      enableAudio: enableAudio ?? this.enableAudio,
      imageQuality: imageQuality ?? this.imageQuality,
      width: width ?? this.width,
      height: height ?? this.height,
      isCircular: isCircular ?? this.isCircular,
      isSquare: isSquare ?? this.isSquare,
      enableAutoInitialization:
          enableAutoInitialization ?? this.enableAutoInitialization,
    );
  }
}
