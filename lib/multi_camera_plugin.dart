/// 多摄像头插件
///
/// 这是一个支持多种摄像头实现方案的Flutter插件，
/// 提供最大的设备兼容性和自动降级功能。
library multi_camera_plugin;

// 导出所有公共API
export 'src/generated/camera_api.g.dart';
export 'src/models/camera_models.dart';
export 'src/managers/multi_camera_manager.dart';

// 导出Widget
export 'src/widgets/multi_camera_preview.dart';

// 导出工具类
export 'src/utils/permission_manager.dart';

import 'package:flutter/material.dart';
import 'src/managers/multi_camera_manager.dart';
import 'src/models/camera_models.dart';

/// 多摄像头插件主类
///
/// 提供简化的API接口，内部使用MultiCameraManager进行管理
class MultiCameraPlugin {
  static MultiCameraPlugin? _instance;
  static MultiCameraPlugin get instance => _instance ??= MultiCameraPlugin._();

  MultiCameraPlugin._();

  /// 获取管理器实例
  MultiCameraManager get manager => MultiCameraManager.instance;

  /// 初始化摄像头
  ///
  /// [config] 摄像头配置
  /// [priority] 实现方案优先级配置
  /// [preferredImplementation] 首选实现方案
  /// [context] BuildContext，用于显示权限对话框
  Future<void> initialize({
    required CameraConfig config,
    CameraImplementationPriority? priority,
    CameraImplementationType? preferredImplementation,
    BuildContext? context,
  }) async {
    return manager.initialize(
      config: config,
      priority: priority,
      preferredImplementation: preferredImplementation,
      context: context,
    );
  }

  /// 启用自动初始化
  ///
  /// 启用后，摄像头将在首次使用时自动初始化，无需手动调用initialize方法
  ///
  /// [config] 摄像头配置
  /// [priority] 实现方案优先级配置
  /// [preferredImplementation] 首选实现方案
  /// [context] BuildContext，用于显示权限对话框
  Future<void> enableAutoInitialization({
    required CameraConfig config,
    CameraImplementationPriority? priority,
    CameraImplementationType? preferredImplementation,
    BuildContext? context,
  }) {
    return manager.enableAutoInitialization(
      config: config,
      priority: priority,
      preferredImplementation: preferredImplementation,
      context: context,
    );
  }

  /// 禁用自动初始化
  void disableAutoInitialization() {
    return manager.disableAutoInitialization();
  }

  /// 拍照
  Future<CaptureResult> takePicture() async {
    return manager.takePicture();
  }

  /// 切换摄像头方向
  Future<void> switchCamera(CameraLensDirection direction) async {
    return manager.switchCamera(direction);
  }

  /// 设置闪光灯模式
  Future<void> setFlashMode(FlashMode mode) async {
    return manager.setFlashMode(mode);
  }

  /// 手动切换实现方案
  Future<void> switchImplementation(
      CameraImplementationType implementationType) async {
    return manager.switchImplementation(implementationType);
  }

  /// 暂停摄像头session
  Future<void> pauseSession() async {
    return manager.pauseSession();
  }

  /// 恢复摄像头session
  Future<void> resumeSession() async {
    return manager.resumeSession();
  }

  /// 释放资源
  Future<void> dispose() async {
    return manager.dispose();
  }

  /// 当前状态
  CameraStatus get status => manager.status;

  /// 当前实现类型
  CameraImplementationType? get currentImplementation =>
      manager.currentImplementation;

  /// 可用的实现方案
  List<CameraImplementationType> get availableImplementations =>
      manager.availableImplementations;

  /// 是否启用了自动初始化
  bool get isAutoInitializationEnabled => manager.isAutoInitializationEnabled;

  /// session是否已暂停
  bool get isSessionPaused => manager.isSessionPaused;

  /// 事件流
  Stream<CameraEvent> get eventStream => manager.eventStream;

  /// 状态流
  Stream<CameraStatus> get statusStream => manager.statusStream;

  /// 错误流
  Stream<CameraError> get errorStream => manager.errorStream;
}
