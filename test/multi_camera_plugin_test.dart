import 'package:flutter_test/flutter_test.dart';
import 'package:multi_camera_plugin/multi_camera_plugin.dart';

void main() {
  group('MultiCameraPlugin', () {
    test('instance creation', () {
      final plugin1 = MultiCameraPlugin.instance;
      final plugin2 = MultiCameraPlugin.instance;

      // 测试单例模式
      expect(plugin1, same(plugin2));
      expect(plugin1.status, CameraStatus.uninitialized);
    });

    test('initial state', () {
      final plugin = MultiCameraPlugin.instance;

      expect(plugin.status, CameraStatus.uninitialized);
      expect(plugin.currentImplementation, isNull);
      expect(plugin.availableImplementations, isEmpty);
    });
  });

  group('CameraConfig', () {
    test('creation with required parameters', () {
      final config = CameraConfig(
        lensDirection: CameraLensDirection.back,
        enableAudio: false,
        imageQuality: 0.8,
        isCircular: false,
        isSquare: false,
        enableAutoInitialization: false,
      );

      expect(config.lensDirection, CameraLensDirection.back);
      expect(config.enableAudio, false);
      expect(config.imageQuality, 0.8);
      expect(config.isCircular, false);
      expect(config.isSquare, false);
      expect(config.enableAutoInitialization, false);
    });

    test('creation with optional parameters', () {
      final config = CameraConfig(
        lensDirection: CameraLensDirection.front,
        enableAudio: true,
        imageQuality: 1.0,
        width: 320,
        height: 240,
        isCircular: true,
        isSquare: false,
        enableAutoInitialization: true,
      );

      expect(config.lensDirection, CameraLensDirection.front);
      expect(config.enableAudio, true);
      expect(config.imageQuality, 1.0);
      expect(config.width, 320);
      expect(config.height, 240);
      expect(config.isCircular, true);
      expect(config.isSquare, false);
      expect(config.enableAutoInitialization, true);
    });
  });

  group('CameraPreviewConfig', () {
    test('default configuration', () {
      const config = CameraPreviewConfig();

      expect(config.width, isNull);
      expect(config.height, isNull);
      expect(config.isCircular, false);
      expect(config.isSquare, false);
      expect(config.borderRadius, 0.0);
      expect(config.showBorder, false);
      expect(config.borderWidth, 1.0);
    });

    test('circular configuration', () {
      final config = CameraPreviewConfig.circular(
        size: 200,
        showBorder: true,
        borderColor: 0xFF2196F3,
        borderWidth: 2.0,
      );

      expect(config.width, 200);
      expect(config.height, 200);
      expect(config.isCircular, true);
      expect(config.showBorder, true);
      expect(config.borderColor, 0xFF2196F3);
      expect(config.borderWidth, 2.0);
    });

    test('square configuration', () {
      final config = CameraPreviewConfig.square(
        size: 300,
        borderRadius: 16.0,
        showBorder: true,
      );

      expect(config.width, 300);
      expect(config.height, 300);
      expect(config.isSquare, true);
      expect(config.borderRadius, 16.0);
      expect(config.showBorder, true);
    });

    test('rectangle configuration', () {
      final config = CameraPreviewConfig.rectangle(
        width: 320,
        height: 240,
        borderRadius: 8.0,
      );

      expect(config.width, 320);
      expect(config.height, 240);
      expect(config.borderRadius, 8.0);
    });
  });

  group('CameraImplementationPriority', () {
    test('default priority', () {
      const priority = CameraImplementationPriority();

      expect(priority.iosPriority, [
        CameraImplementationType.native,
        CameraImplementationType.flutterCamera,
      ]);

      expect(priority.androidPriority, [
        CameraImplementationType.flutterCamera,
        CameraImplementationType.native,
      ]);

      expect(priority.enableAutoFallback, true);
      expect(priority.maxRetryCount, 2);
      expect(priority.retryDelayMs, 500);
      expect(priority.enableSmartErrorHandling, true);
    });

    test('custom priority', () {
      const priority = CameraImplementationPriority(
        iosPriority: [CameraImplementationType.flutterCamera],
        androidPriority: [CameraImplementationType.native],
        enableAutoFallback: false,
        maxRetryCount: 1,
      );

      expect(priority.iosPriority, [CameraImplementationType.flutterCamera]);
      expect(priority.androidPriority, [CameraImplementationType.native]);
      expect(priority.enableAutoFallback, false);
      expect(priority.maxRetryCount, 1);
    });
  });

  group('ErrorClassifier', () {
    test('classifies permission errors as severe', () {
      expect(
          ErrorClassifier.classifyError(
              'PERMISSION_DENIED', 'Permission denied'),
          ErrorSeverity.severe);
      expect(ErrorClassifier.classifyError('AUTH_ERROR', 'not authorized'),
          ErrorSeverity.severe);
    });

    test('classifies camera in use errors as minor', () {
      expect(ErrorClassifier.classifyError('CAMERA_IN_USE', 'Camera is busy'),
          ErrorSeverity.minor);
      expect(ErrorClassifier.classifyError('BUSY_ERROR', 'device occupied'),
          ErrorSeverity.minor);
    });

    test('classifies hardware errors as moderate', () {
      expect(
          ErrorClassifier.classifyError('HARDWARE_ERROR', 'Hardware failure'),
          ErrorSeverity.moderate);
      expect(
          ErrorClassifier.classifyError('INITIALIZATION_FAILED', 'Init failed'),
          ErrorSeverity.moderate);
      expect(
          ErrorClassifier.classifyError('BLACK_SCREEN', 'Camera black screen'),
          ErrorSeverity.moderate);
    });

    test('classifies unknown errors as moderate by default', () {
      expect(
          ErrorClassifier.classifyError(
              'UNKNOWN_ERROR', 'Something went wrong'),
          ErrorSeverity.moderate);
    });
  });
}
