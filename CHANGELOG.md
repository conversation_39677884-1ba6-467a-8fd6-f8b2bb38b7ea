## 0.1.4

### 🎯 新增功能：增强自动降级机制 + 权限管理

* **初始化阶段自动降级**
  - 在`initialize()`方法中增加自动降级逻辑
  - 当首选实现方案初始化失败时，自动尝试其他可用方案
  - 用户完全无感知，确保摄像头功能始终可用

* **预览创建失败自动降级**
  - 在预览Widget构建时增加错误处理和自动降级
  - 当预览创建失败时，自动切换到备选实现方案
  - 提供无缝的用户体验，避免摄像头无法使用的情况

* **智能错误处理**
  - 新增`_tryInitializationFallback()`方法，专门处理初始化阶段的降级
  - 新增`_handlePreviewError()`方法，处理预览错误并触发降级
  - 复用现有的`_tryFallbackImplementation()`基础设施，保持代码一致性

* **🔐 权限管理系统**
  - 新增`PermissionManager`类，提供完整的权限管理功能
  - 自动检查和请求相机权限，支持麦克风权限（录音功能）
  - 智能处理权限被拒绝、永久拒绝等情况
  - 提供友好的用户引导对话框，支持直接跳转到系统设置
  - 在初始化前自动进行权限检查，避免权限问题导致的初始化失败

* **🚀 用户体验优化**
  - 权限被拒绝时显示系统级对话框，引导用户开启权限
  - 权限被永久拒绝时自动跳转到系统设置页面
  - 在示例应用中增加"检查权限"按钮，方便测试和调试
  - 详细的权限状态显示，包括相机权限和麦克风权限

* **配置和控制**
  - 完全兼容现有的`CameraImplementationPriority`配置
  - 支持通过`enableAutoFallback`开关控制是否启用自动降级
  - 支持通过`maxRetryCount`控制最大重试次数，避免无限重试
  - 所有初始化方法新增`context`参数，用于显示权限对话框

* **测试和文档**
  - 增加了详细的自动降级测试用例和文档
  - 包含初始化失败、预览失败、配置测试等多个场景
  - 提供了完整的测试矩阵和性能要求
  - 新增权限管理相关的文档和示例代码

## 0.1.3

### ⚠️ 重要变更：移除 CameraAwesome 支持

* **兼容性改进**
  - 由于 CameraAwesome 插件与当前 Flutter SDK 版本存在严重兼容性问题，已从插件中完全移除
  - 移除了`CameraImplementationType.cameraAwesome`枚举值
  - 删除了`camera_awesome_implementation.dart`实现文件
  - 从`pubspec.yaml`中移除了`camerawesome`依赖项

* **API简化**
  - 更新了`CameraImplementationPriority`默认配置，只包含两种实现方案
  - 简化了所有switch语句，移除了CameraAwesome相关分支
  - 更新了示例代码和文档，移除了CameraAwesome相关引用

* **稳定性提升**
  - 现在插件专注于两个稳定可靠的实现方案：
    - **Flutter Camera**: 官方支持，兼容性好，适合一般场景
    - **原生实现**: 性能最佳，功能完整，适合高性能场景
  - 所有核心功能保持不变：拍照、切换摄像头、闪光灯控制、Session暂停/恢复等

* **测试和构建**
  - 修复了所有编译错误和警告
  - 更新了单元测试，移除了CameraAwesome相关测试案例
  - 验证了Android APK构建成功
  - 确保所有测试通过（11/11）

* **文档更新**
  - 更新了README.md，移除了CameraAwesome相关说明
  - 更新了API文档和使用示例
  - 修改了实现方案对比表格
  - 调整了平台优先级建议

## 0.1.2

### 🔥 新增功能：Session暂停和恢复

* **Session控制功能**
  - 新增`pauseSession()`方法，支持暂停摄像头预览以冻结画面
  - 新增`resumeSession()`方法，支持恢复摄像头预览以继续实时显示
  - 完美支持人脸识别、照片分析等业务场景：拍照完成后暂停预览进行后台处理，处理完成后恢复预览

* **跨平台实现统一**
  - **iOS原生实现**：通过`AVCaptureSession.stopRunning()`/`startRunning()`实现真正的预览暂停/恢复
  - **Android原生实现**：通过`cameraProvider.unbindAll()`/重新绑定用例实现预览暂停/恢复
  - **Flutter Camera实现**：通过`CameraController.pausePreview()`/`resumePreview()`实现预览暂停/恢复

* **UI优化改进**
  - 优化路径显示：拍照成功消息和最后拍照路径只显示一行，优先显示文件名，长路径用省略号处理
  - 修复iOS原生实现双层边框问题，与Flutter Camera实现保持视觉一致
  - 改进状态管理，session暂停时保持预览界面显示

* **状态管理增强**
  - 新增`CameraStatus.sessionPaused`状态
  - 新增`CameraSessionPausedEvent`和`CameraSessionResumedEvent`事件
  - 移除拍照时的状态变更，保持预览界面稳定性

* **示例应用完善**
  - 在示例应用中添加了session暂停/恢复按钮
  - 优化了路径显示的用户体验
  - 改进了UI布局和交互逻辑

## 0.1.1

### 🚀 新增功能：自动初始化逻辑

* **自动初始化支持**
  - 新增自动初始化功能，无需手动调用初始化方法即可使用摄像头
  - 在首次调用拍照或切换摄像头时自动触发初始化流程
  - 提供`enableAutoInitialization()`和`disableAutoInitialization()`方法控制自动初始化行为

* **增强的事件系统**
  - 新增`AutoInitializationStartedEvent`事件，监听自动初始化开始
  - 新增`AutoInitializationCompletedEvent`事件，监听自动初始化完成状态
  - 改进了事件流处理，提供更好的用户体验

* **示例应用改进**
  - 在示例应用中增加了自动初始化和手动初始化的对比演示
  - 优化了UI布局，支持圆形和正方形预览切换
  - 增强了错误处理和状态显示

* **配置选项扩展**
  - 在`CameraConfig`中新增`enableAutoInitialization`参数
  - 支持在预览组件中直接启用自动初始化功能

## 0.1.0

### 🎉 重大修复：Android 预览拉伸问题

* **修复Android原生实现的预览画面拉伸变形问题**
  - 解决了Android端预览图像"宽度拉长、高度压缩约1/3"的严重显示问题
  - 采用了与flutter_camera插件相同的技术方案：在Dart层面交换相机传感器的宽高数据
  - 确保在方形、圆形等自定义容器中预览比例完全正确，无任何变形

* **技术改进**
  - 在`native_implementation.dart`中实现了相机传感器横向输出与UI竖向显示的正确适配
  - 使用`FittedBox`和`BoxFit.cover`确保预览在各种尺寸容器中完美缩放
  - 添加了详细的调试日志，便于问题排查

* **代码质量提升**
  - 为Android原生实现添加了详细的中文注释，解释技术方案的原理
  - 优化了CameraX的配置逻辑，确保旋转设置正确生效
  - 简化了预览视图的实现，提高了代码可维护性

### 🔧 其他改进

* **iOS原生实现保持稳定**
  - iOS端预览效果本身正常，无需修改
  - 继续使用AVFoundation + UIKit实现

* **API接口优化**
  - 完善了CameraTexture数据结构，包含正确的分辨率信息
  - 改进了错误处理和状态管理逻辑

## 0.0.1

* 初始版本发布
* 基础的多摄像头功能实现
* 支持iOS和Android原生实现
* Flutter Camera插件集成
