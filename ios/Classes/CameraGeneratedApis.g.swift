// Autogenerated from <PERSON>eon (v22.7.2), do not edit directly.
// See also: https://pub.dev/packages/pigeon

import Foundation

#if os(iOS)
  import Flutter
#elseif os(macOS)
  import FlutterMacOS
#else
  #error("Unsupported platform.")
#endif

/// Error class for passing custom error details to Dart side.
final class PigeonError: Error {
  let code: String
  let message: String?
  let details: Any?

  init(code: String, message: String?, details: Any?) {
    self.code = code
    self.message = message
    self.details = details
  }

  var localizedDescription: String {
    return
      "PigeonError(code: \(code), message: \(message ?? "<nil>"), details: \(details ?? "<nil>")"
      }
}

private func wrapResult(_ result: Any?) -> [Any?] {
  return [result]
}

private func wrapError(_ error: Any) -> [Any?] {
  if let pigeonError = error as? PigeonError {
    return [
      pigeonError.code,
      pigeonError.message,
      pigeonError.details,
    ]
  }
  if let flutterError = error as? FlutterError {
    return [
      flutterError.code,
      flutterError.message,
      flutterError.details,
    ]
  }
  return [
    "\(error)",
    "\(type(of: error))",
    "Stacktrace: \(Thread.callStackSymbols)",
  ]
}

private func createConnectionError(withChannelName channelName: String) -> PigeonError {
  return PigeonError(code: "channel-error", message: "Unable to establish connection on channel: '\(channelName)'.", details: "")
}

private func isNullish(_ value: Any?) -> Bool {
  return value is NSNull || value == nil
}

private func nilOrValue<T>(_ value: Any?) -> T? {
  if value is NSNull { return nil }
  return value as! T?
}

/// 摄像头实现类型枚举
enum CameraImplementationType: Int {
  /// Flutter Camera插件实现
  case flutterCamera = 0
  /// 原生实现
  case native = 1
}

/// 摄像头状态枚举
enum CameraStatus: Int {
  /// 未初始化
  case uninitialized = 0
  /// 初始化中
  case initializing = 1
  /// 已初始化
  case initialized = 2
  /// 拍照中
  case takingPicture = 3
  /// session已暂停
  case sessionPaused = 4
  /// 错误状态
  case error = 5
  /// 已释放
  case disposed = 6
}

/// 摄像头方向枚举
enum CameraLensDirection: Int {
  /// 前置摄像头
  case front = 0
  /// 后置摄像头
  case back = 1
  /// 外部摄像头
  case external = 2
}

/// 闪光灯模式枚举
enum FlashMode: Int {
  /// 关闭
  case off = 0
  /// 开启
  case on = 1
  /// 自动
  case auto = 2
  /// 常亮
  case torch = 3
}

/// 摄像头配置
///
/// Generated class from Pigeon that represents data sent in messages.
struct CameraConfig {
  /// 摄像头方向
  var lensDirection: CameraLensDirection
  /// 是否启用音频
  var enableAudio: Bool
  /// 图片质量 (0.0-1.0)
  var imageQuality: Double
  /// 摄像头窗口宽度
  var width: Double? = nil
  /// 摄像头窗口高度
  var height: Double? = nil
  /// 是否为圆形窗口
  var isCircular: Bool
  /// 是否为正方形窗口
  var isSquare: Bool
  /// 是否启用自动初始化
  var enableAutoInitialization: Bool


  // swift-format-ignore: AlwaysUseLowerCamelCase
  static func fromList(_ pigeonVar_list: [Any?]) -> CameraConfig? {
    let lensDirection = pigeonVar_list[0] as! CameraLensDirection
    let enableAudio = pigeonVar_list[1] as! Bool
    let imageQuality = pigeonVar_list[2] as! Double
    let width: Double? = nilOrValue(pigeonVar_list[3])
    let height: Double? = nilOrValue(pigeonVar_list[4])
    let isCircular = pigeonVar_list[5] as! Bool
    let isSquare = pigeonVar_list[6] as! Bool
    let enableAutoInitialization = pigeonVar_list[7] as! Bool

    return CameraConfig(
      lensDirection: lensDirection,
      enableAudio: enableAudio,
      imageQuality: imageQuality,
      width: width,
      height: height,
      isCircular: isCircular,
      isSquare: isSquare,
      enableAutoInitialization: enableAutoInitialization
    )
  }
  func toList() -> [Any?] {
    return [
      lensDirection,
      enableAudio,
      imageQuality,
      width,
      height,
      isCircular,
      isSquare,
      enableAutoInitialization,
    ]
  }
}

/// 拍照结果
///
/// Generated class from Pigeon that represents data sent in messages.
struct CaptureResult {
  /// 图片文件路径
  var imagePath: String
  /// 拍照时间戳
  var timestamp: Int64
  /// 图片大小（字节）
  var fileSize: Int64


  // swift-format-ignore: AlwaysUseLowerCamelCase
  static func fromList(_ pigeonVar_list: [Any?]) -> CaptureResult? {
    let imagePath = pigeonVar_list[0] as! String
    let timestamp = pigeonVar_list[1] as! Int64
    let fileSize = pigeonVar_list[2] as! Int64

    return CaptureResult(
      imagePath: imagePath,
      timestamp: timestamp,
      fileSize: fileSize
    )
  }
  func toList() -> [Any?] {
    return [
      imagePath,
      timestamp,
      fileSize,
    ]
  }
}

/// 摄像头错误信息
///
/// Generated class from Pigeon that represents data sent in messages.
struct CameraError {
  /// 错误代码
  var code: String
  /// 错误消息
  var message: String
  /// 错误详情
  var details: String? = nil


  // swift-format-ignore: AlwaysUseLowerCamelCase
  static func fromList(_ pigeonVar_list: [Any?]) -> CameraError? {
    let code = pigeonVar_list[0] as! String
    let message = pigeonVar_list[1] as! String
    let details: String? = nilOrValue(pigeonVar_list[2])

    return CameraError(
      code: code,
      message: message,
      details: details
    )
  }
  func toList() -> [Any?] {
    return [
      code,
      message,
      details,
    ]
  }
}

/// 摄像头能力信息
///
/// Generated class from Pigeon that represents data sent in messages.
struct CameraCapabilities {
  /// 支持的实现类型列表
  var supportedImplementations: [CameraImplementationType]
  /// 是否支持前置摄像头
  var hasFrontCamera: Bool
  /// 是否支持后置摄像头
  var hasBackCamera: Bool
  /// 是否支持闪光灯
  var hasFlash: Bool
  /// 是否支持变焦
  var hasZoom: Bool


  // swift-format-ignore: AlwaysUseLowerCamelCase
  static func fromList(_ pigeonVar_list: [Any?]) -> CameraCapabilities? {
    let supportedImplementations = pigeonVar_list[0] as! [CameraImplementationType]
    let hasFrontCamera = pigeonVar_list[1] as! Bool
    let hasBackCamera = pigeonVar_list[2] as! Bool
    let hasFlash = pigeonVar_list[3] as! Bool
    let hasZoom = pigeonVar_list[4] as! Bool

    return CameraCapabilities(
      supportedImplementations: supportedImplementations,
      hasFrontCamera: hasFrontCamera,
      hasBackCamera: hasBackCamera,
      hasFlash: hasFlash,
      hasZoom: hasZoom
    )
  }
  func toList() -> [Any?] {
    return [
      supportedImplementations,
      hasFrontCamera,
      hasBackCamera,
      hasFlash,
      hasZoom,
    ]
  }
}

/// Generated class from Pigeon that represents data sent in messages.
struct CameraTexture {
  var textureId: Int64? = nil
  var width: Double? = nil
  var height: Double? = nil


  // swift-format-ignore: AlwaysUseLowerCamelCase
  static func fromList(_ pigeonVar_list: [Any?]) -> CameraTexture? {
    let textureId: Int64? = nilOrValue(pigeonVar_list[0])
    let width: Double? = nilOrValue(pigeonVar_list[1])
    let height: Double? = nilOrValue(pigeonVar_list[2])

    return CameraTexture(
      textureId: textureId,
      width: width,
      height: height
    )
  }
  func toList() -> [Any?] {
    return [
      textureId,
      width,
      height,
    ]
  }
}

private class CameraGeneratedApisPigeonCodecReader: FlutterStandardReader {
  override func readValue(ofType type: UInt8) -> Any? {
    switch type {
    case 129:
      let enumResultAsInt: Int? = nilOrValue(self.readValue() as! Int?)
      if let enumResultAsInt = enumResultAsInt {
        return CameraImplementationType(rawValue: enumResultAsInt)
      }
      return nil
    case 130:
      let enumResultAsInt: Int? = nilOrValue(self.readValue() as! Int?)
      if let enumResultAsInt = enumResultAsInt {
        return CameraStatus(rawValue: enumResultAsInt)
      }
      return nil
    case 131:
      let enumResultAsInt: Int? = nilOrValue(self.readValue() as! Int?)
      if let enumResultAsInt = enumResultAsInt {
        return CameraLensDirection(rawValue: enumResultAsInt)
      }
      return nil
    case 132:
      let enumResultAsInt: Int? = nilOrValue(self.readValue() as! Int?)
      if let enumResultAsInt = enumResultAsInt {
        return FlashMode(rawValue: enumResultAsInt)
      }
      return nil
    case 133:
      return CameraConfig.fromList(self.readValue() as! [Any?])
    case 134:
      return CaptureResult.fromList(self.readValue() as! [Any?])
    case 135:
      return CameraError.fromList(self.readValue() as! [Any?])
    case 136:
      return CameraCapabilities.fromList(self.readValue() as! [Any?])
    case 137:
      return CameraTexture.fromList(self.readValue() as! [Any?])
    default:
      return super.readValue(ofType: type)
    }
  }
}

private class CameraGeneratedApisPigeonCodecWriter: FlutterStandardWriter {
  override func writeValue(_ value: Any) {
    if let value = value as? CameraImplementationType {
      super.writeByte(129)
      super.writeValue(value.rawValue)
    } else if let value = value as? CameraStatus {
      super.writeByte(130)
      super.writeValue(value.rawValue)
    } else if let value = value as? CameraLensDirection {
      super.writeByte(131)
      super.writeValue(value.rawValue)
    } else if let value = value as? FlashMode {
      super.writeByte(132)
      super.writeValue(value.rawValue)
    } else if let value = value as? CameraConfig {
      super.writeByte(133)
      super.writeValue(value.toList())
    } else if let value = value as? CaptureResult {
      super.writeByte(134)
      super.writeValue(value.toList())
    } else if let value = value as? CameraError {
      super.writeByte(135)
      super.writeValue(value.toList())
    } else if let value = value as? CameraCapabilities {
      super.writeByte(136)
      super.writeValue(value.toList())
    } else if let value = value as? CameraTexture {
      super.writeByte(137)
      super.writeValue(value.toList())
    } else {
      super.writeValue(value)
    }
  }
}

private class CameraGeneratedApisPigeonCodecReaderWriter: FlutterStandardReaderWriter {
  override func reader(with data: Data) -> FlutterStandardReader {
    return CameraGeneratedApisPigeonCodecReader(data: data)
  }

  override func writer(with data: NSMutableData) -> FlutterStandardWriter {
    return CameraGeneratedApisPigeonCodecWriter(data: data)
  }
}

class CameraGeneratedApisPigeonCodec: FlutterStandardMessageCodec, @unchecked Sendable {
  static let shared = CameraGeneratedApisPigeonCodec(readerWriter: CameraGeneratedApisPigeonCodecReaderWriter())
}


/// 主机端API接口（Flutter调用原生）
///
/// Generated protocol from Pigeon that represents a handler of messages from Flutter.
protocol MultiCameraHostApi {
  /// 获取摄像头能力
  func getCameraCapabilities(completion: @escaping (Result<CameraCapabilities, Error>) -> Void)
  /// 初始化摄像头
  func initializeCamera(config: CameraConfig, implementationType: CameraImplementationType, completion: @escaping (Result<Void, Error>) -> Void)
  /// 切换实现方式
  func switchImplementation(implementationType: CameraImplementationType, completion: @escaping (Result<Void, Error>) -> Void)
  /// 切换摄像头方向（前置/后置）
  func switchCamera(direction: CameraLensDirection, completion: @escaping (Result<Void, Error>) -> Void)
  /// 拍照
  func takePicture(completion: @escaping (Result<CaptureResult, Error>) -> Void)
  /// 设置闪光灯模式
  func setFlashMode(mode: FlashMode, completion: @escaping (Result<Void, Error>) -> Void)
  /// 开始预览
  func startPreview(completion: @escaping (Result<Void, Error>) -> Void)
  /// 停止预览
  func stopPreview(completion: @escaping (Result<Void, Error>) -> Void)
  /// 释放摄像头资源
  func dispose(completion: @escaping (Result<Void, Error>) -> Void)
  /// 检查特定实现是否可用
  func isImplementationAvailable(implementationType: CameraImplementationType, completion: @escaping (Result<Bool, Error>) -> Void)
  /// Creates a texture for rendering the camera preview and returns its ID.
  func createCameraTexture(completion: @escaping (Result<CameraTexture, Error>) -> Void)
  /// 暂停摄像头session
  func pauseSession(completion: @escaping (Result<Void, Error>) -> Void)
  /// 恢复摄像头session
  func resumeSession(completion: @escaping (Result<Void, Error>) -> Void)
  /// 检查session是否已暂停
  func isSessionPaused(completion: @escaping (Result<Bool, Error>) -> Void)
  /// 原生权限检测（iOS/Android原生方法）
  func checkCameraPermissionNative(completion: @escaping (Result<Bool, Error>) -> Void)
}

/// Generated setup class from Pigeon to handle messages through the `binaryMessenger`.
class MultiCameraHostApiSetup {
  static var codec: FlutterStandardMessageCodec { CameraGeneratedApisPigeonCodec.shared }
  /// Sets up an instance of `MultiCameraHostApi` to handle messages through the `binaryMessenger`.
  static func setUp(binaryMessenger: FlutterBinaryMessenger, api: MultiCameraHostApi?, messageChannelSuffix: String = "") {
    let channelSuffix = messageChannelSuffix.count > 0 ? ".\(messageChannelSuffix)" : ""
    /// 获取摄像头能力
    let getCameraCapabilitiesChannel = FlutterBasicMessageChannel(name: "dev.flutter.pigeon.multi_camera_plugin.MultiCameraHostApi.getCameraCapabilities\(channelSuffix)", binaryMessenger: binaryMessenger, codec: codec)
    if let api = api {
      getCameraCapabilitiesChannel.setMessageHandler { _, reply in
        api.getCameraCapabilities { result in
          switch result {
          case .success(let res):
            reply(wrapResult(res))
          case .failure(let error):
            reply(wrapError(error))
          }
        }
      }
    } else {
      getCameraCapabilitiesChannel.setMessageHandler(nil)
    }
    /// 初始化摄像头
    let initializeCameraChannel = FlutterBasicMessageChannel(name: "dev.flutter.pigeon.multi_camera_plugin.MultiCameraHostApi.initializeCamera\(channelSuffix)", binaryMessenger: binaryMessenger, codec: codec)
    if let api = api {
      initializeCameraChannel.setMessageHandler { message, reply in
        let args = message as! [Any?]
        let configArg = args[0] as! CameraConfig
        let implementationTypeArg = args[1] as! CameraImplementationType
        api.initializeCamera(config: configArg, implementationType: implementationTypeArg) { result in
          switch result {
          case .success:
            reply(wrapResult(nil))
          case .failure(let error):
            reply(wrapError(error))
          }
        }
      }
    } else {
      initializeCameraChannel.setMessageHandler(nil)
    }
    /// 切换实现方式
    let switchImplementationChannel = FlutterBasicMessageChannel(name: "dev.flutter.pigeon.multi_camera_plugin.MultiCameraHostApi.switchImplementation\(channelSuffix)", binaryMessenger: binaryMessenger, codec: codec)
    if let api = api {
      switchImplementationChannel.setMessageHandler { message, reply in
        let args = message as! [Any?]
        let implementationTypeArg = args[0] as! CameraImplementationType
        api.switchImplementation(implementationType: implementationTypeArg) { result in
          switch result {
          case .success:
            reply(wrapResult(nil))
          case .failure(let error):
            reply(wrapError(error))
          }
        }
      }
    } else {
      switchImplementationChannel.setMessageHandler(nil)
    }
    /// 切换摄像头方向（前置/后置）
    let switchCameraChannel = FlutterBasicMessageChannel(name: "dev.flutter.pigeon.multi_camera_plugin.MultiCameraHostApi.switchCamera\(channelSuffix)", binaryMessenger: binaryMessenger, codec: codec)
    if let api = api {
      switchCameraChannel.setMessageHandler { message, reply in
        let args = message as! [Any?]
        let directionArg = args[0] as! CameraLensDirection
        api.switchCamera(direction: directionArg) { result in
          switch result {
          case .success:
            reply(wrapResult(nil))
          case .failure(let error):
            reply(wrapError(error))
          }
        }
      }
    } else {
      switchCameraChannel.setMessageHandler(nil)
    }
    /// 拍照
    let takePictureChannel = FlutterBasicMessageChannel(name: "dev.flutter.pigeon.multi_camera_plugin.MultiCameraHostApi.takePicture\(channelSuffix)", binaryMessenger: binaryMessenger, codec: codec)
    if let api = api {
      takePictureChannel.setMessageHandler { _, reply in
        api.takePicture { result in
          switch result {
          case .success(let res):
            reply(wrapResult(res))
          case .failure(let error):
            reply(wrapError(error))
          }
        }
      }
    } else {
      takePictureChannel.setMessageHandler(nil)
    }
    /// 设置闪光灯模式
    let setFlashModeChannel = FlutterBasicMessageChannel(name: "dev.flutter.pigeon.multi_camera_plugin.MultiCameraHostApi.setFlashMode\(channelSuffix)", binaryMessenger: binaryMessenger, codec: codec)
    if let api = api {
      setFlashModeChannel.setMessageHandler { message, reply in
        let args = message as! [Any?]
        let modeArg = args[0] as! FlashMode
        api.setFlashMode(mode: modeArg) { result in
          switch result {
          case .success:
            reply(wrapResult(nil))
          case .failure(let error):
            reply(wrapError(error))
          }
        }
      }
    } else {
      setFlashModeChannel.setMessageHandler(nil)
    }
    /// 开始预览
    let startPreviewChannel = FlutterBasicMessageChannel(name: "dev.flutter.pigeon.multi_camera_plugin.MultiCameraHostApi.startPreview\(channelSuffix)", binaryMessenger: binaryMessenger, codec: codec)
    if let api = api {
      startPreviewChannel.setMessageHandler { _, reply in
        api.startPreview { result in
          switch result {
          case .success:
            reply(wrapResult(nil))
          case .failure(let error):
            reply(wrapError(error))
          }
        }
      }
    } else {
      startPreviewChannel.setMessageHandler(nil)
    }
    /// 停止预览
    let stopPreviewChannel = FlutterBasicMessageChannel(name: "dev.flutter.pigeon.multi_camera_plugin.MultiCameraHostApi.stopPreview\(channelSuffix)", binaryMessenger: binaryMessenger, codec: codec)
    if let api = api {
      stopPreviewChannel.setMessageHandler { _, reply in
        api.stopPreview { result in
          switch result {
          case .success:
            reply(wrapResult(nil))
          case .failure(let error):
            reply(wrapError(error))
          }
        }
      }
    } else {
      stopPreviewChannel.setMessageHandler(nil)
    }
    /// 释放摄像头资源
    let disposeChannel = FlutterBasicMessageChannel(name: "dev.flutter.pigeon.multi_camera_plugin.MultiCameraHostApi.dispose\(channelSuffix)", binaryMessenger: binaryMessenger, codec: codec)
    if let api = api {
      disposeChannel.setMessageHandler { _, reply in
        api.dispose { result in
          switch result {
          case .success:
            reply(wrapResult(nil))
          case .failure(let error):
            reply(wrapError(error))
          }
        }
      }
    } else {
      disposeChannel.setMessageHandler(nil)
    }
    /// 检查特定实现是否可用
    let isImplementationAvailableChannel = FlutterBasicMessageChannel(name: "dev.flutter.pigeon.multi_camera_plugin.MultiCameraHostApi.isImplementationAvailable\(channelSuffix)", binaryMessenger: binaryMessenger, codec: codec)
    if let api = api {
      isImplementationAvailableChannel.setMessageHandler { message, reply in
        let args = message as! [Any?]
        let implementationTypeArg = args[0] as! CameraImplementationType
        api.isImplementationAvailable(implementationType: implementationTypeArg) { result in
          switch result {
          case .success(let res):
            reply(wrapResult(res))
          case .failure(let error):
            reply(wrapError(error))
          }
        }
      }
    } else {
      isImplementationAvailableChannel.setMessageHandler(nil)
    }
    /// Creates a texture for rendering the camera preview and returns its ID.
    let createCameraTextureChannel = FlutterBasicMessageChannel(name: "dev.flutter.pigeon.multi_camera_plugin.MultiCameraHostApi.createCameraTexture\(channelSuffix)", binaryMessenger: binaryMessenger, codec: codec)
    if let api = api {
      createCameraTextureChannel.setMessageHandler { _, reply in
        api.createCameraTexture { result in
          switch result {
          case .success(let res):
            reply(wrapResult(res))
          case .failure(let error):
            reply(wrapError(error))
          }
        }
      }
    } else {
      createCameraTextureChannel.setMessageHandler(nil)
    }
    /// 暂停摄像头session
    let pauseSessionChannel = FlutterBasicMessageChannel(name: "dev.flutter.pigeon.multi_camera_plugin.MultiCameraHostApi.pauseSession\(channelSuffix)", binaryMessenger: binaryMessenger, codec: codec)
    if let api = api {
      pauseSessionChannel.setMessageHandler { _, reply in
        api.pauseSession { result in
          switch result {
          case .success:
            reply(wrapResult(nil))
          case .failure(let error):
            reply(wrapError(error))
          }
        }
      }
    } else {
      pauseSessionChannel.setMessageHandler(nil)
    }
    /// 恢复摄像头session
    let resumeSessionChannel = FlutterBasicMessageChannel(name: "dev.flutter.pigeon.multi_camera_plugin.MultiCameraHostApi.resumeSession\(channelSuffix)", binaryMessenger: binaryMessenger, codec: codec)
    if let api = api {
      resumeSessionChannel.setMessageHandler { _, reply in
        api.resumeSession { result in
          switch result {
          case .success:
            reply(wrapResult(nil))
          case .failure(let error):
            reply(wrapError(error))
          }
        }
      }
    } else {
      resumeSessionChannel.setMessageHandler(nil)
    }
    /// 检查session是否已暂停
    let isSessionPausedChannel = FlutterBasicMessageChannel(name: "dev.flutter.pigeon.multi_camera_plugin.MultiCameraHostApi.isSessionPaused\(channelSuffix)", binaryMessenger: binaryMessenger, codec: codec)
    if let api = api {
      isSessionPausedChannel.setMessageHandler { _, reply in
        api.isSessionPaused { result in
          switch result {
          case .success(let res):
            reply(wrapResult(res))
          case .failure(let error):
            reply(wrapError(error))
          }
        }
      }
    } else {
      isSessionPausedChannel.setMessageHandler(nil)
    }
    /// 原生权限检测（iOS/Android原生方法）
    let checkCameraPermissionNativeChannel = FlutterBasicMessageChannel(name: "dev.flutter.pigeon.multi_camera_plugin.MultiCameraHostApi.checkCameraPermissionNative\(channelSuffix)", binaryMessenger: binaryMessenger, codec: codec)
    if let api = api {
      checkCameraPermissionNativeChannel.setMessageHandler { _, reply in
        api.checkCameraPermissionNative { result in
          switch result {
          case .success(let res):
            reply(wrapResult(res))
          case .failure(let error):
            reply(wrapError(error))
          }
        }
      }
    } else {
      checkCameraPermissionNativeChannel.setMessageHandler(nil)
    }
  }
}
/// Flutter端API接口（原生调用Flutter）
///
/// Generated protocol from Pigeon that represents Flutter messages that can be called from Swift.
protocol MultiCameraFlutterApiProtocol {
  /// 摄像头状态变化回调
  func onCameraStatusChanged(status statusArg: CameraStatus, completion: @escaping (Result<Void, PigeonError>) -> Void)
  /// 摄像头错误回调
  func onCameraError(error errorArg: CameraError, completion: @escaping (Result<Void, PigeonError>) -> Void)
  /// 实现方案自动切换回调
  func onImplementationSwitched(newImplementation newImplementationArg: CameraImplementationType, reason reasonArg: String, completion: @escaping (Result<Void, PigeonError>) -> Void)
}
class MultiCameraFlutterApi: MultiCameraFlutterApiProtocol {
  private let binaryMessenger: FlutterBinaryMessenger
  private let messageChannelSuffix: String
  init(binaryMessenger: FlutterBinaryMessenger, messageChannelSuffix: String = "") {
    self.binaryMessenger = binaryMessenger
    self.messageChannelSuffix = messageChannelSuffix.count > 0 ? ".\(messageChannelSuffix)" : ""
  }
  var codec: CameraGeneratedApisPigeonCodec {
    return CameraGeneratedApisPigeonCodec.shared
  }
  /// 摄像头状态变化回调
  func onCameraStatusChanged(status statusArg: CameraStatus, completion: @escaping (Result<Void, PigeonError>) -> Void) {
    let channelName: String = "dev.flutter.pigeon.multi_camera_plugin.MultiCameraFlutterApi.onCameraStatusChanged\(messageChannelSuffix)"
    let channel = FlutterBasicMessageChannel(name: channelName, binaryMessenger: binaryMessenger, codec: codec)
    channel.sendMessage([statusArg] as [Any?]) { response in
      guard let listResponse = response as? [Any?] else {
        completion(.failure(createConnectionError(withChannelName: channelName)))
        return
      }
      if listResponse.count > 1 {
        let code: String = listResponse[0] as! String
        let message: String? = nilOrValue(listResponse[1])
        let details: String? = nilOrValue(listResponse[2])
        completion(.failure(PigeonError(code: code, message: message, details: details)))
      } else {
        completion(.success(Void()))
      }
    }
  }
  /// 摄像头错误回调
  func onCameraError(error errorArg: CameraError, completion: @escaping (Result<Void, PigeonError>) -> Void) {
    let channelName: String = "dev.flutter.pigeon.multi_camera_plugin.MultiCameraFlutterApi.onCameraError\(messageChannelSuffix)"
    let channel = FlutterBasicMessageChannel(name: channelName, binaryMessenger: binaryMessenger, codec: codec)
    channel.sendMessage([errorArg] as [Any?]) { response in
      guard let listResponse = response as? [Any?] else {
        completion(.failure(createConnectionError(withChannelName: channelName)))
        return
      }
      if listResponse.count > 1 {
        let code: String = listResponse[0] as! String
        let message: String? = nilOrValue(listResponse[1])
        let details: String? = nilOrValue(listResponse[2])
        completion(.failure(PigeonError(code: code, message: message, details: details)))
      } else {
        completion(.success(Void()))
      }
    }
  }
  /// 实现方案自动切换回调
  func onImplementationSwitched(newImplementation newImplementationArg: CameraImplementationType, reason reasonArg: String, completion: @escaping (Result<Void, PigeonError>) -> Void) {
    let channelName: String = "dev.flutter.pigeon.multi_camera_plugin.MultiCameraFlutterApi.onImplementationSwitched\(messageChannelSuffix)"
    let channel = FlutterBasicMessageChannel(name: channelName, binaryMessenger: binaryMessenger, codec: codec)
    channel.sendMessage([newImplementationArg, reasonArg] as [Any?]) { response in
      guard let listResponse = response as? [Any?] else {
        completion(.failure(createConnectionError(withChannelName: channelName)))
        return
      }
      if listResponse.count > 1 {
        let code: String = listResponse[0] as! String
        let message: String? = nilOrValue(listResponse[1])
        let details: String? = nilOrValue(listResponse[2])
        completion(.failure(PigeonError(code: code, message: message, details: details)))
      } else {
        completion(.success(Void()))
      }
    }
  }
}
