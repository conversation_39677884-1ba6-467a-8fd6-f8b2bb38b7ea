# Multi Camera Plugin

一个支持多种摄像头实现方案的Flutter插件，提供最大的设备兼容性和自动降级功能。

## 🎉 最新更新 (v0.1.4)

### 🎯 增强自动降级机制 + 权限管理系统

#### ✨ 全新的自动降级功能
我们大幅增强了自动降级机制，确保摄像头功能在任何情况下都能正常工作：

- **初始化阶段自动降级**: 当首选实现方案初始化失败时，自动尝试其他可用方案，用户完全无感知
- **预览创建失败自动降级**: 预览Widget构建时如果出现错误，自动切换到备选实现方案
- **智能错误处理**: 新增专门的错误处理方法，提供无缝的降级体验
- **配置灵活性**: 支持通过`enableAutoFallback`和`maxRetryCount`控制降级行为

#### 🔐 完整的权限管理系统
全新的`PermissionManager`类提供了一站式权限解决方案：

- **自动权限检查**: 在初始化前自动检查相机和麦克风权限
- **智能权限请求**: 根据配置需求智能请求必要权限
- **友好用户引导**: 权限被拒绝时显示系统级对话框，引导用户开启权限
- **系统设置跳转**: 权限被永久拒绝时自动跳转到系统设置页面
- **详细状态反馈**: 提供完整的权限状态信息和错误处理

#### 🚀 用户体验优化
- 所有初始化方法新增`context`参数，用于显示权限对话框
- 示例应用中增加"检查权限"按钮，方便测试和调试
- 完全兼容现有的配置系统，无需修改现有代码
- 详细的权限状态显示，包括相机权限和麦克风权限

### ⚠️ 重要变更：移除 CameraAwesome 支持

由于 CameraAwesome 插件与当前 Flutter SDK 版本存在兼容性问题，我们已从插件中移除 CameraAwesome 支持。现在插件专注于两个稳定可靠的实现方案：

- **✅ Flutter Camera**: 官方支持，稳定性好，文档完善
- **✅ 原生实现**: 性能最佳，完全控制，支持高级功能

这一变更确保了插件的稳定性和可维护性，同时保持了所有核心功能不变。

### ✅ Android 预览拉伸问题已完全解决

我们彻底解决了Android原生实现中的预览画面拉伸变形问题！现在Android端的预览效果与iOS端完全一致：

- **修复前**: Android预览在方形/圆形容器中出现"宽度拉长、高度压缩约1/3"的严重变形
- **修复后**: 预览比例完全正确，无任何拉伸或压缩，与iOS效果一致
- **技术方案**: 采用了与`flutter_camera`插件相同的解决思路，在Dart层面正确处理相机传感器的横向输出与UI竖向显示的适配问题

> 💡 **技术细节**: 相机传感器通常输出横向图像（如1920x1080），但Flutter UI会将其旋转90度显示。我们通过在Dart层面交换宽高数据，确保容器尺寸与实际显示完美匹配，从而消除了拉伸变形。

这个修复让Android原生实现真正可用于生产环境，特别是在需要自定义预览形状的场景中。

## 功能特性

### 🎯 核心功能
- **双重实现方案**: 支持 Flutter Camera 和原生实现
- **增强自动降级**: 初始化和预览阶段都支持自动降级，确保摄像头始终可用
- **完整权限管理**: 自动权限检查、智能请求、友好引导、系统设置跳转
- **手动切换**: 允许用户手动选择摄像头实现方案
- **统一API**: 提供一致的API接口，屏蔽底层实现差异

### 📷 摄像头功能
- **拍照**: 高质量图片捕获
- **前后摄像头切换**: 支持前置和后置摄像头
- **闪光灯控制**: 支持关闭、开启、自动、常亮模式
- **预览窗口**: 可自定义大小、形状（圆形、正方形、矩形）
- **实时预览**: 流畅的摄像头预览
- **Session控制**: 支持暂停/恢复预览（真正冻结预览画面）

### 🔧 高级特性
- **事件监听**: 状态变化、错误、实现方案切换等事件
- **智能权限管理**: 自动检查、请求、引导用户开启权限
- **增强错误处理**: 自动降级、智能重试、无缝用户体验
- **用户偏好**: 自动保存和恢复用户选择的实现方案
- **平台优化**: 针对iOS和Android的不同优化策略

## 安装

在 `pubspec.yaml` 中添加依赖：

```yaml
dependencies:
  multi_camera_plugin: ^1.0.0
```

然后运行：

```bash
flutter pub get
```

## 快速开始

### 1. 基本使用

```dart
import 'package:multi_camera_plugin/multi_camera_plugin.dart';

class CameraPage extends StatefulWidget {
  @override
  _CameraPageState createState() => _CameraPageState();
}

class _CameraPageState extends State<CameraPage> {
  final MultiCameraPlugin _plugin = MultiCameraPlugin.instance;

  @override
  void initState() {
    super.initState();
    _initializeCamera();
  }

  Future<void> _initializeCamera() async {
    final config = CameraConfig(
      lensDirection: CameraLensDirection.back,
      enableAudio: false,
      imageQuality: 0.8,
      isCircular: false,
      isSquare: false,
    );

    // 0.1.4版本新增：自动权限管理
    await _plugin.initialize(
      config: config,
      context: context, // 用于显示权限对话框
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: MultiCameraPreview(
        cameraConfig: CameraConfig(
          lensDirection: CameraLensDirection.back,
          enableAudio: false,
          imageQuality: 0.8,
          isCircular: false,
          isSquare: false,
        ),
        previewConfig: const CameraPreviewConfig(),
        onCameraReady: () {
          print('摄像头已就绪');
        },
        onError: (error) {
          print('摄像头错误: ${error.message}');
        },
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () async {
          try {
            final result = await _plugin.takePicture();
            print('拍照成功: ${result.imagePath}');
          } catch (e) {
            print('拍照失败: $e');
          }
        },
        child: const Icon(Icons.camera),
      ),
    );
  }
}
```

### 2. 高级配置

```dart
// 自定义实现方案优先级（0.1.4版本增强）
final priority = CameraImplementationPriority(
  iosPriority: [
    CameraImplementationType.native,
    CameraImplementationType.flutterCamera,
  ],
  androidPriority: [
    CameraImplementationType.flutterCamera,
    CameraImplementationType.native,
  ],
  enableAutoFallback: true,  // 启用自动降级
  maxRetryCount: 3,          // 最大重试次数
);

// 初始化时指定优先级和首选实现（新增context参数）
await _plugin.initialize(
  config: config,
  context: context,          // 用于权限对话框
  priority: priority,
  preferredImplementation: CameraImplementationType.flutterCamera,
);
```

### 3. 自定义预览窗口

```dart
// 圆形预览窗口
MultiCameraPreview(
  cameraConfig: config,
  previewConfig: CameraPreviewConfig.circular(
    size: 200,
    showBorder: true,
    borderColor: 0xFF2196F3,
    borderWidth: 2.0,
  ),
)

// 正方形预览窗口
MultiCameraPreview(
  cameraConfig: config,
  previewConfig: CameraPreviewConfig.square(
    size: 300,
    borderRadius: 16.0,
    showBorder: true,
  ),
)

// 矩形预览窗口
MultiCameraPreview(
  cameraConfig: config,
  previewConfig: CameraPreviewConfig.rectangle(
    width: 320,
    height: 240,
    borderRadius: 8.0,
  ),
)
```

### 4. 权限管理

插件提供了完整的权限管理功能，自动处理相机权限检查和请求：

```dart
import 'package:multi_camera_plugin/multi_camera_plugin.dart';

// 自动权限处理（推荐）
// 初始化时会自动检查和请求权限
await _plugin.initialize(
  config: config,
  context: context, // 用于显示权限对话框
);

// 手动权限检查
final result = await PermissionManager.handleCameraPermission(
  context,
  enableAudio: false, // 是否需要麦克风权限
  showDialog: true,   // 是否显示权限引导对话框
);

if (result.isGranted) {
  print('权限已获取');
} else if (result.needsManualSettings) {
  print('需要手动到设置中开启权限');
  // 自动跳转到系统设置
  await PermissionManager.openSystemSettings();
} else if (result.userDenied) {
  print('用户拒绝了权限');
}

// 检查特定权限状态
final cameraStatus = await PermissionManager.checkCameraPermission();
final micStatus = await PermissionManager.checkMicrophonePermission();
```

**权限处理特性：**
- ✅ 自动检查相机和麦克风权限
- ✅ 智能处理权限被拒绝的情况
- ✅ 友好的用户引导对话框
- ✅ 自动跳转到系统设置页面
- ✅ 支持权限被永久拒绝的处理

### 5. 事件监听

```dart
// 监听状态变化
_plugin.statusStream.listen((status) {
  print('摄像头状态: $status');
});

// 监听错误
_plugin.errorStream.listen((error) {
  print('摄像头错误: ${error.message}');
});

// 监听事件
_plugin.eventStream.listen((event) {
  if (event is CameraImplementationSwitchedEvent) {
    print('实现方案已切换: ${event.oldImplementation} → ${event.newImplementation}');
    print('切换原因: ${event.reason}');
  } else if (event is CameraCaptureCompletedEvent) {
    print('拍照完成: ${event.result.imagePath}');
  }
});
```

## API 参考

### MultiCameraPlugin

主要的插件类，提供所有摄像头功能。

#### 方法

- `initialize()`: 初始化摄像头
- `takePicture()`: 拍照
- `switchCamera()`: 切换摄像头方向
- `setFlashMode()`: 设置闪光灯模式
- `switchImplementation()`: 手动切换实现方案
- `dispose()`: 释放资源

#### 属性

- `status`: 当前状态
- `currentImplementation`: 当前实现类型
- `availableImplementations`: 可用的实现方案
- `eventStream`: 事件流
- `statusStream`: 状态流
- `errorStream`: 错误流

### MultiCameraPreview

摄像头预览Widget，提供丰富的自定义选项。

#### 参数

- `cameraConfig`: 摄像头配置
- `previewConfig`: 预览配置
- `priority`: 实现方案优先级配置
- `preferredImplementation`: 首选实现方案
- `onCameraReady`: 摄像头准备就绪回调
- `onError`: 错误回调
- `onStatusChanged`: 状态变化回调
- `onImplementationSwitched`: 实现方案切换回调
- `showStatusIndicator`: 是否显示状态指示器
- `showImplementationIndicator`: 是否显示实现方案指示器
- `showErrorMessage`: 是否显示错误信息

### PermissionManager

权限管理工具类，提供完整的权限检查和请求功能。

#### 静态方法

- `handleCameraPermission()`: 综合权限处理，包括检查、请求和用户引导
- `checkCameraPermission()`: 检查相机权限状态
- `requestCameraPermission()`: 请求相机权限
- `checkMicrophonePermission()`: 检查麦克风权限状态
- `requestMicrophonePermission()`: 请求麦克风权限
- `openSystemSettings()`: 打开系统设置页面
- `showPermissionDialog()`: 显示权限说明对话框
- `showPermanentlyDeniedDialog()`: 显示权限被永久拒绝的对话框

#### CameraPermissionResult

权限处理结果类，包含详细的权限状态信息：

- `isGranted`: 是否获得了使用相机所需的全部权限
- `hasCameraPermission`: 是否有相机权限
- `hasMicrophonePermission`: 是否有麦克风权限（如果需要的话）
- `needsManualSettings`: 是否需要用户手动到设置中开启
- `userDenied`: 用户是否主动拒绝了权限
- `hasError`: 是否有错误
- `errorMessage`: 错误信息

## 实现方案对比

| 实现方案 | 优势 | 劣势 | 推荐场景 | 状态 |
|---------|------|------|----------|-------|
| **原生实现** | 性能最佳、完全控制、支持session控制 | 开发复杂度高 | 对性能要求极高的场景 | ✅ 完整实现 |
| **Flutter Camera** | 官方支持、稳定性好、文档完善 | 功能相对简单 | 一般应用场景 | ✅ 完整实现 |

### 🔄 自动切换优先级

**iOS平台默认优先级：**
1. 原生实现 (最佳性能)
2. Flutter Camera (官方支持)

**Android平台默认优先级：**
1. Flutter Camera (稳定可靠)
2. 原生实现 (最佳性能)

### 📱 Session控制支持

两种实现都支持session暂停/恢复功能：
- ✅ **原生实现**: 完整的session控制
- ✅ **Flutter Camera**: 基于Controller的session控制

当session暂停时，摄像头预览会真正冻结（不动态更新），确保预览画面完全静止。

## 平台支持

- ✅ iOS 12.0+
- ✅ Android API 21+
- 🚧 macOS (开发中)
- ❌ Web (暂不支持)
- ❌ Windows (暂不支持)
- ❌ Linux (暂不支持)

## 原生实现技术栈

### Android
- **主要方案**: CameraX (Google推荐的现代摄像头库)
- **降级方案**: Camera2 API (Android 5.0+)
- **兼容性**: API 21+ (Android 5.0+)
- **特性**:
  - 自动处理生命周期
  - 优秀的设备兼容性
  - 内置图像分析功能
  - 支持HDR和夜间模式

### iOS
- **技术栈**: AVFoundation框架
- **兼容性**: iOS 12.0+
- **特性**:
  - 原生性能最佳
  - 完整的摄像头控制
  - 支持所有iOS摄像头功能
  - 低延迟预览

## 权限配置

### Android
在 `android/app/src/main/AndroidManifest.xml` 中添加：

```xml
<uses-permission android:name="android.permission.CAMERA" />
<uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
<uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />

<uses-feature android:name="android.hardware.camera" android:required="false" />
<uses-feature android:name="android.hardware.camera.front" android:required="false" />
<uses-feature android:name="android.hardware.camera.autofocus" android:required="false" />
<uses-feature android:name="android.hardware.camera.flash" android:required="false" />
```

### iOS
在 `ios/Runner/Info.plist` 中添加：

```xml
<key>NSCameraUsageDescription</key>
<string>此应用需要访问摄像头来拍照</string>
<key>NSPhotoLibraryUsageDescription</key>
<string>此应用需要访问相册来保存照片</string>
```

## 性能优化建议

### 1. 实现方案选择策略
```dart
// 高性能场景：优先使用原生实现
final priority = CameraImplementationPriority(
  iosPriority: [
    CameraImplementationType.native,
    CameraImplementationType.flutterCamera,
  ],
  androidPriority: [
    CameraImplementationType.native,
    CameraImplementationType.flutterCamera,
  ],
);

// 稳定性优先场景：优先使用官方插件
final priority = CameraImplementationPriority(
  iosPriority: [
    CameraImplementationType.flutterCamera,
    CameraImplementationType.native,
  ],
  androidPriority: [
    CameraImplementationType.flutterCamera,
    CameraImplementationType.native,
  ],
);
```

### 2. 内存管理
```dart
// 及时释放资源
@override
void dispose() {
  MultiCameraPlugin.instance.dispose();
  super.dispose();
}

// 应用进入后台时停止预览
@override
void didChangeAppLifecycleState(AppLifecycleState state) {
  if (state == AppLifecycleState.paused) {
    MultiCameraPlugin.instance.manager.stopPreview();
  } else if (state == AppLifecycleState.resumed) {
    MultiCameraPlugin.instance.manager.startPreview();
  }
}
```

### 3. 错误处理最佳实践
```dart
// 监听错误并自动重试
plugin.errorStream.listen((error) {
  if (error.code == 'CAMERA_ERROR') {
    // 尝试切换到备用实现
    plugin.switchImplementation(CameraImplementationType.flutterCamera);
  }
});

// 实现自定义错误恢复策略
class CameraErrorHandler {
  static Future<void> handleError(CameraError error) async {
    switch (error.code) {
      case 'PERMISSION_DENIED':
        await _requestPermissions();
        break;
      case 'CAMERA_IN_USE':
        await _waitAndRetry();
        break;
      case 'HARDWARE_ERROR':
        await _switchImplementation();
        break;
    }
  }
}
```

## 故障排除

### 常见问题

1. **摄像头黑屏**
   - 检查权限是否已授予
   - 尝试切换实现方案
   - 重启应用

2. **拍照失败**
   - 确保存储权限已授予
   - 检查存储空间是否充足
   - 验证输出目录是否存在

3. **预览卡顿**
   - 降低预览分辨率
   - 关闭不必要的图像处理
   - 使用原生实现

4. **兼容性问题**
   - 启用自动降级功能
   - 配置多个实现方案
   - 测试目标设备

### 调试技巧

```dart
// 启用详细日志
MultiCameraPlugin.instance.eventStream.listen((event) {
  print('Camera Event: $event');
});

// 检查设备能力
final capabilities = await plugin.manager.getCameraCapabilities();
print('Available implementations: ${capabilities.supportedImplementations}');
print('Has front camera: ${capabilities.hasFrontCamera}');
print('Has flash: ${capabilities.hasFlash}');
```

## 贡献指南

欢迎提交Issue和Pull Request！

### 开发环境设置
1. Clone项目
2. 运行 `flutter pub get`
3. 运行 `dart run pigeon --input pigeons/camera_api.dart` 生成API代码
4. 运行示例应用测试功能

### 代码规范
- 遵循Dart官方代码规范
- 添加完整的文档注释
- 编写单元测试和集成测试
- 确保所有平台都能正常工作

## 许可证

MIT License

## 更新日志

### v0.1.4
- 🎯 增强自动降级机制（初始化和预览阶段）
- 🔐 完整的权限管理系统（PermissionManager类）
- ✅ 智能错误处理和自动恢复
- ✅ 友好的用户权限引导对话框
- ✅ 自动跳转到系统设置功能
- ✅ 详细的权限状态反馈
- ✅ 示例应用权限测试功能

### v0.1.3
- ⚠️ 移除CameraAwesome支持（兼容性问题）
- ✅ 保持Flutter Camera和原生实现
- ✅ 简化API和配置选项
- ✅ 更新文档和示例代码
- ✅ 完整的测试覆盖

### v0.1.2
- ✅ Session暂停和恢复功能
- ✅ 路径显示优化
- ✅ UI界面改进
- ✅ 状态管理增强

### v0.1.1
- ✅ 自动初始化逻辑
- ✅ 增强的事件系统
- ✅ 示例应用改进

### v0.1.0
- ✅ Android 预览拉伸问题修复
- ✅ 原生Android实现（CameraX）
- ✅ 原生iOS实现（AVFoundation）
- ✅ Flutter Camera插件集成
- ✅ 智能自动降级机制
- ✅ 完整的API文档和示例

